# 📚 TRAINING FOLDER

## 📋 <PERSON><PERSON><PERSON> đích
Th<PERSON> mục này chứa các notebook training cho model SmartTrash.

## 📁 Nội dung
- **SmartTrash_Training.ipynb**: Notebook training chính trên Google Colab
- **SmartTrash_Simple_Training.ipynb**: <PERSON><PERSON>n bản đơn gi<PERSON>n
- **requirements_colab.txt**: Dependencies cho Google Colab

## 🚀 Hướng dẫn sử dụng

### 1. Upload lên Google Colab
1. Mở Google Colab: https://colab.research.google.com/
2. Upload notebook (.ipynb)
3. Upload dataset hoặc mount Google Drive
4. Chạy từng cell theo thứ tự

### 2. Training Process
1. **Setup Environment**: Install dependencies
2. **Load Dataset**: 10,138 ảnh, 10 classes
3. **Data Preprocessing**: Resize, normalize, augmentation
4. **Model Training**: MobileNetV2 Transfer Learning
5. **Evaluation**: Test accuracy, confusion matrix
6. **Export**: Model.h5, labels.txt, model_info.json

### 3. Download Results
- `smarttrash_model.h5`: Trained model
- `labels.txt`: Class names
- `model_info.json`: Model metadata
- `training_history.png`: Training plots

## 📊 Expected Results
- **Training Time**: 30-45 phút trên GPU T4
- **Accuracy**: >90% trên test set
- **Model Size**: ~10MB
- **Classes**: 10 loại rác

## 🔗 Links
- **Google Colab**: https://colab.research.google.com/
- **Dataset**: ../rubbish-data/
- **Models**: ../models/
- **Web App**: ../web-app/

---
**Lưu ý**: Cần GPU để training hiệu quả. Sử dụng Google Colab miễn phí với GPU T4.
