#!/usr/bin/env python3
"""
Audio Generator for SmartTrash
Tạo audio feedback cho kết quả phân loại
"""

import os
import uuid
from datetime import datetime
import json
import requests
import base64

# Try to import text-to-speech libraries
try:
    from gtts import gTTS
    GTTS_AVAILABLE = True
except ImportError:
    GTTS_AVAILABLE = False
    print("⚠️ gTTS not available. Audio generation will be limited.")

try:
    import pyttsx3
    PYTTSX3_AVAILABLE = True
except ImportError:
    PYTTSX3_AVAILABLE = False
    print("⚠️ pyttsx3 not available. Offline TTS not available.")

# Gemini API configuration
GEMINI_API_KEY = "AIzaSyCWx5TBOd8fvmt0OGMq5kkHcL2XTr9dvy8"
GEMINI_TTS_MODEL = "gemini-2.0-flash-exp"  # Model hỗ trợ TTS
GEMINI_AVAILABLE = True if GEMINI_API_KEY else False

class AudioGenerator:
    def __init__(self, audio_dir='static/audio'):
        """
        Initialize audio generator
        
        Args:
            audio_dir: Directory to save audio files
        """
        self.audio_dir = audio_dir
        self.audio_cache = {}
        
        # Create audio directory
        os.makedirs(audio_dir, exist_ok=True)
        
        # Initialize offline TTS if available
        self.offline_engine = None
        if PYTTSX3_AVAILABLE:
            try:
                self.offline_engine = pyttsx3.init()
                # Set properties
                self.offline_engine.setProperty('rate', 150)  # Speed
                self.offline_engine.setProperty('volume', 0.9)  # Volume
                print("✅ Offline TTS engine initialized")
            except Exception as e:
                print(f"⚠️ Could not initialize offline TTS: {e}")
        
        print(f"✅ Audio generator initialized (dir: {audio_dir})")
    
    def generate_audio(self, class_name, method='auto'):
        """
        Generate audio for waste class

        Args:
            class_name: Name of waste class
            method: 'web_speech', 'gtts', 'offline', or 'auto'

        Returns:
            str: Path to generated audio file or None
        """
        try:
            # Check cache first
            cache_key = f"{class_name}_{method}"
            if cache_key in self.audio_cache:
                audio_path = self.audio_cache[cache_key]
                if os.path.exists(audio_path):
                    return audio_path

            # Generate text
            text = self._generate_text(class_name)

            # Choose method - prioritize web speech for better Vietnamese support
            if method == 'auto':
                # Use web speech as primary method (handled by frontend)
                return self._create_web_speech_data(class_name, text)
            elif method == 'web_speech':
                return self._create_web_speech_data(class_name, text)
            elif method == 'gtts' and GTTS_AVAILABLE:
                audio_path = self._generate_gtts(text, class_name)
            elif method == 'offline' and PYTTSX3_AVAILABLE:
                audio_path = self._generate_offline(text, class_name)
            else:
                return self._create_web_speech_data(class_name, text)

            # Cache result
            if audio_path:
                self.audio_cache[cache_key] = audio_path

            return audio_path

        except Exception as e:
            print(f"Error generating audio for {class_name}: {e}")
            return self._create_web_speech_data(class_name, self._generate_text(class_name))
    
    def _get_waste_group(self, class_name):
        """Get waste group for class"""
        waste_groups = {
            # Rác tái chế khô
            'paper': 'rác tái chế khô',
            'cardboard': 'rác tái chế khô',
            'plastic': 'rác tái chế khô',
            'metal': 'rác tái chế khô',
            'white-glass': 'rác tái chế khô',
            'green-glass': 'rác tái chế khô',
            'brown-glass': 'rác tái chế khô',

            # Rác hữu cơ
            'biological': 'rác hữu cơ',

            # Rác đặc biệt
            'battery': 'rác đặc biệt',

            # Rác không tái chế
            'trash': 'rác không tái chế'
        }
        return waste_groups.get(class_name, 'rác không xác định')

    def _generate_text(self, class_name):
        """Generate Vietnamese text for class"""

        # Get waste group
        waste_group = self._get_waste_group(class_name)

        # Vietnamese names
        vietnamese_names = {
            'battery': 'pin',
            'biological': 'rác hữu cơ',
            'brown-glass': 'thủy tinh nâu',
            'cardboard': 'bìa carton',
            'green-glass': 'thủy tinh xanh',
            'metal': 'kim loại',
            'paper': 'giấy',
            'plastic': 'nhựa',
            'trash': 'rác thông thường',
            'white-glass': 'thủy tinh trắng'
        }

        item_name = vietnamese_names.get(class_name, class_name)

        # Generate text based on new format
        text = f"Đây là {item_name}, thuộc nhóm {waste_group}. Hãy bỏ vào thùng chứa {waste_group}."

        return text

    def _create_web_speech_data(self, class_name, text):
        """Create data for Web Speech API (handled by frontend)"""
        try:
            # Create a JSON file with speech data for frontend
            filename = f"speech_{class_name}.json"
            filepath = os.path.join(self.audio_dir, filename)

            speech_data = {
                'type': 'web_speech',
                'class': class_name,
                'text': text,
                'lang': 'vi-VN',
                'rate': 0.9,
                'pitch': 1.0,
                'volume': 1.0,
                'timestamp': datetime.now().isoformat()
            }

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(speech_data, f, ensure_ascii=False, indent=2)

            print(f"✅ Created Web Speech data: {filename}")
            return f"/static/audio/{filename}"

        except Exception as e:
            print(f"Error creating Web Speech data: {e}")
            return None

    def _generate_gemini_tts(self, text, class_name):
        """Generate audio using Gemini API TTS"""
        try:
            # Create filename
            filename = f"gemini_{class_name}_{uuid.uuid4().hex[:8]}.wav"
            filepath = os.path.join(self.audio_dir, filename)

            # Gemini API endpoint
            url = f"https://generativelanguage.googleapis.com/v1beta/models/{GEMINI_TTS_MODEL}:generateContent"

            headers = {
                'Content-Type': 'application/json',
            }

            # Request payload for TTS
            payload = {
                "contents": [{
                    "parts": [{
                        "text": f"Hãy đọc văn bản sau bằng tiếng Việt với giọng nói tự nhiên: {text}"
                    }]
                }],
                "generationConfig": {
                    "response_modalities": ["AUDIO"],
                    "speech_config": {
                        "voice_config": {
                            "prebuilt_voice_config": {
                                "voice_name": "Aoede"  # Voice name for Vietnamese
                            }
                        }
                    }
                }
            }

            # Make API request
            response = requests.post(
                f"{url}?key={GEMINI_API_KEY}",
                headers=headers,
                json=payload,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()

                # Extract audio data from response
                if 'candidates' in result and len(result['candidates']) > 0:
                    candidate = result['candidates'][0]
                    if 'content' in candidate and 'parts' in candidate['content']:
                        for part in candidate['content']['parts']:
                            if 'inline_data' in part and 'data' in part['inline_data']:
                                # Decode base64 audio data
                                audio_data = base64.b64decode(part['inline_data']['data'])

                                # Save audio file
                                with open(filepath, 'wb') as f:
                                    f.write(audio_data)

                                print(f"✅ Generated Gemini TTS audio: {filename}")
                                return f"/static/audio/{filename}"

                print("⚠️ No audio data in Gemini response")
                return None
            else:
                print(f"❌ Gemini API error: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            print(f"Error generating Gemini TTS audio: {e}")
            return None

    def _generate_gtts(self, text, class_name):
        """Generate audio using Google Text-to-Speech"""
        try:
            # Create filename
            filename = f"gtts_{class_name}_{uuid.uuid4().hex[:8]}.mp3"
            filepath = os.path.join(self.audio_dir, filename)
            
            # Generate TTS
            tts = gTTS(text=text, lang='vi', slow=False)
            tts.save(filepath)
            
            print(f"✅ Generated gTTS audio: {filename}")
            return f"/static/audio/{filename}"
            
        except Exception as e:
            print(f"Error generating gTTS audio: {e}")
            return None
    
    def _generate_offline(self, text, class_name):
        """Generate audio using offline TTS"""
        try:
            if not self.offline_engine:
                return None
            
            # Create filename
            filename = f"offline_{class_name}_{uuid.uuid4().hex[:8]}.wav"
            filepath = os.path.join(self.audio_dir, filename)
            
            # Generate TTS
            self.offline_engine.save_to_file(text, filepath)
            self.offline_engine.runAndWait()
            
            print(f"✅ Generated offline TTS audio: {filename}")
            return f"/static/audio/{filename}"
            
        except Exception as e:
            print(f"Error generating offline TTS audio: {e}")
            return None
    
    def _create_fallback_audio(self, class_name):
        """Create fallback audio info when TTS is not available"""
        # Return a JSON with text instead of audio
        fallback_info = {
            'type': 'text_fallback',
            'class': class_name,
            'text': self._generate_text(class_name),
            'timestamp': datetime.now().isoformat()
        }
        
        filename = f"fallback_{class_name}.json"
        filepath = os.path.join(self.audio_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(fallback_info, f, ensure_ascii=False, indent=2)
        
        return f"/static/audio/{filename}"
    
    def generate_custom_audio(self, text, filename_prefix='custom'):
        """
        Generate audio for custom text
        
        Args:
            text: Text to convert to speech
            filename_prefix: Prefix for filename
        
        Returns:
            str: Path to generated audio file
        """
        try:
            if GTTS_AVAILABLE:
                filename = f"{filename_prefix}_{uuid.uuid4().hex[:8]}.mp3"
                filepath = os.path.join(self.audio_dir, filename)
                
                tts = gTTS(text=text, lang='vi', slow=False)
                tts.save(filepath)
                
                return f"/static/audio/{filename}"
            
            elif PYTTSX3_AVAILABLE and self.offline_engine:
                filename = f"{filename_prefix}_{uuid.uuid4().hex[:8]}.wav"
                filepath = os.path.join(self.audio_dir, filename)
                
                self.offline_engine.save_to_file(text, filepath)
                self.offline_engine.runAndWait()
                
                return f"/static/audio/{filename}"
            
            else:
                return self._create_fallback_audio('custom')
                
        except Exception as e:
            print(f"Error generating custom audio: {e}")
            return None
    
    def cleanup_old_files(self, max_age_hours=24):
        """
        Clean up old audio files
        
        Args:
            max_age_hours: Maximum age of files to keep (in hours)
        """
        try:
            import time
            current_time = time.time()
            max_age_seconds = max_age_hours * 3600
            
            cleaned_count = 0
            
            for filename in os.listdir(self.audio_dir):
                filepath = os.path.join(self.audio_dir, filename)
                
                if os.path.isfile(filepath):
                    file_age = current_time - os.path.getctime(filepath)
                    
                    if file_age > max_age_seconds:
                        os.remove(filepath)
                        cleaned_count += 1
                        
                        # Remove from cache
                        keys_to_remove = [k for k, v in self.audio_cache.items() if v.endswith(filename)]
                        for key in keys_to_remove:
                            del self.audio_cache[key]
            
            if cleaned_count > 0:
                print(f"🧹 Cleaned up {cleaned_count} old audio files")
                
        except Exception as e:
            print(f"Error cleaning up audio files: {e}")
    
    def get_audio_info(self):
        """Get information about audio generation capabilities"""
        return {
            'gtts_available': GTTS_AVAILABLE,
            'offline_tts_available': PYTTSX3_AVAILABLE,
            'audio_directory': self.audio_dir,
            'cached_files': len(self.audio_cache),
            'supported_languages': ['vi'] if GTTS_AVAILABLE else []
        }
