{% extends "base.html" %}

{% block title %}SmartTrash - Camera{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-12 text-center mb-4">
            <h1>
                <i class="fas fa-camera me-2"></i>
                Camera Phân loại Rác
            </h1>
            <p class="lead text-muted">
                Sử dụng camera để nhận diện và phân loại rác real-time
            </p>
        </div>
    </div>
    
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="camera-container">
                <div class="card border-0 shadow-lg">
                    <div class="card-body p-0">
                        <video id="cameraVideo" class="camera-video" autoplay muted playsinline></video>
                        
                        <div class="camera-controls p-4">
                            <button id="captureBtn" class="capture-btn">
                                <i class="fas fa-camera"></i>
                            </button>
                            <p class="mt-3 mb-0 text-muted">
                                Nhấn nút để chụp ảnh và phân tích
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-lightbulb me-2"></i>
                        Hướng dẫn sử dụng
                    </h5>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="d-flex align-items-start">
                                <div class="flex-shrink-0">
                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <strong>1</strong>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6>Chuẩn bị</h6>
                                    <p class="mb-0 small text-muted">
                                        Đặt vật phẩm rác cần phân loại trước camera
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <div class="d-flex align-items-start">
                                <div class="flex-shrink-0">
                                    <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <strong>2</strong>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6>Chụp ảnh</h6>
                                    <p class="mb-0 small text-muted">
                                        Nhấn nút camera để chụp ảnh
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <div class="d-flex align-items-start">
                                <div class="flex-shrink-0">
                                    <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <strong>3</strong>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6>Xem kết quả</h6>
                                    <p class="mb-0 small text-muted">
                                        Hệ thống sẽ phân tích và đưa ra kết quả
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Results will be inserted here by JavaScript -->
<div id="resultsContainer"></div>
{% endblock %}

{% block extra_css %}
<style>
.camera-video {
    width: 100%;
    max-width: 640px;
    height: auto;
    border-radius: 15px 15px 0 0;
}

.camera-controls {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 0 0 15px 15px;
}

.capture-btn {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 4px solid white;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.capture-btn:hover {
    background: linear-gradient(135deg, #20c997, #17a2b8);
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(40, 167, 69, 0.4);
}

.capture-btn:active {
    transform: scale(0.95);
}

@media (max-width: 768px) {
    .capture-btn {
        width: 60px;
        height: 60px;
        font-size: 20px;
    }
}
</style>
{% endblock %}
