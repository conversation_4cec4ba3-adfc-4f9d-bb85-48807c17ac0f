// SmartTrash Main JavaScript

// Global variables
let currentStream = null;
let isProcessing = false;

// Initialize when document is ready
$(document).ready(function() {
    console.log('SmartTrash app initialized');
    
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize file upload handlers
    initializeFileUpload();
    
    // Initialize camera if on camera page
    if (window.location.pathname.includes('/camera')) {
        initializeCamera();
    }
});

// File Upload Functions
function initializeFileUpload() {
    const uploadArea = $('#uploadArea');
    const fileInput = $('#fileInput');
    
    if (uploadArea.length) {
        // Click to upload
        uploadArea.on('click', function() {
            fileInput.click();
        });
        
        // File input change
        fileInput.on('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                handleFileUpload(file);
            }
        });
        
        // Drag and drop
        uploadArea.on('dragover', function(e) {
            e.preventDefault();
            $(this).addClass('dragover');
        });
        
        uploadArea.on('dragleave', function(e) {
            e.preventDefault();
            $(this).removeClass('dragover');
        });
        
        uploadArea.on('drop', function(e) {
            e.preventDefault();
            $(this).removeClass('dragover');
            
            const files = e.originalEvent.dataTransfer.files;
            if (files.length > 0) {
                handleFileUpload(files[0]);
            }
        });
    }
}

function handleFileUpload(file) {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp'];
    if (!allowedTypes.includes(file.type)) {
        showAlert('Vui lòng chọn file ảnh hợp lệ (JPG, PNG, GIF, BMP)', 'error');
        return;
    }
    
    // Validate file size (16MB max)
    if (file.size > 16 * 1024 * 1024) {
        showAlert('File quá lớn. Vui lòng chọn file nhỏ hơn 16MB', 'error');
        return;
    }
    
    // Create FormData and upload
    const formData = new FormData();
    formData.append('image', file);
    
    uploadImage(formData);
}

function uploadImage(formData) {
    if (isProcessing) return;
    
    isProcessing = true;
    showLoading(true);
    
    $.ajax({
        url: '/api/predict',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            displayResults(response);

            // Auto-play audio after showing results
            if (response.audio_url) {
                setTimeout(() => {
                    playAudio(response.audio_url);
                }, 800); // Small delay to let UI update first
            }

            showAlert('Phân tích ảnh thành công!', 'success');
        },
        error: function(xhr, status, error) {
            console.error('Upload error:', error);
            const errorMsg = xhr.responseJSON ? xhr.responseJSON.error : 'Có lỗi xảy ra khi phân tích ảnh';
            showAlert(errorMsg, 'error');
        },
        complete: function() {
            isProcessing = false;
            showLoading(false);
        }
    });
}

// Camera Functions
function initializeCamera() {
    const video = document.getElementById('cameraVideo');
    const captureBtn = document.getElementById('captureBtn');
    
    if (video && captureBtn) {
        startCamera();
        
        captureBtn.addEventListener('click', function() {
            captureImage();
        });
    }
}

async function startCamera() {
    try {
        // Check if getUserMedia is supported
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            throw new Error('Camera API not supported in this browser');
        }

        // Request camera permission with fallback options
        let stream;
        try {
            // Try with ideal constraints first
            stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    width: { ideal: 640, min: 320 },
                    height: { ideal: 480, min: 240 },
                    facingMode: 'environment' // Use back camera on mobile
                }
            });
        } catch (error) {
            console.warn('Failed with ideal constraints, trying basic:', error);
            // Fallback to basic video constraint
            stream = await navigator.mediaDevices.getUserMedia({
                video: true
            });
        }

        const video = document.getElementById('cameraVideo');
        if (!video) {
            throw new Error('Video element not found');
        }

        video.srcObject = stream;
        currentStream = stream;

        // Wait for video to be ready
        video.onloadedmetadata = function() {
            console.log('Camera started successfully');
            showAlert('Camera đã sẵn sàng!', 'success');
        };

        // Handle video errors
        video.onerror = function(error) {
            console.error('Video error:', error);
            showAlert('Lỗi hiển thị video camera', 'error');
        };

    } catch (error) {
        console.error('Error accessing camera:', error);

        let errorMessage = 'Không thể truy cập camera. ';

        if (error.name === 'NotAllowedError') {
            errorMessage += 'Vui lòng cho phép truy cập camera trong trình duyệt.';
        } else if (error.name === 'NotFoundError') {
            errorMessage += 'Không tìm thấy camera trên thiết bị.';
        } else if (error.name === 'NotReadableError') {
            errorMessage += 'Camera đang được sử dụng bởi ứng dụng khác.';
        } else if (error.name === 'OverconstrainedError') {
            errorMessage += 'Camera không hỗ trợ độ phân giải yêu cầu.';
        } else {
            errorMessage += 'Lỗi không xác định: ' + error.message;
        }

        showAlert(errorMessage, 'error');

        // Show instructions for enabling camera
        showCameraInstructions();
    }
}

function showCameraInstructions() {
    const instructionsHtml = `
        <div class="alert alert-info mt-3">
            <h6><i class="fas fa-info-circle me-2"></i>Hướng dẫn bật camera:</h6>
            <ul class="mb-0">
                <li><strong>Chrome/Edge:</strong> Nhấn vào biểu tượng camera trong thanh địa chỉ</li>
                <li><strong>Firefox:</strong> Nhấn "Allow" khi được hỏi</li>
                <li><strong>Mobile:</strong> Kiểm tra cài đặt quyền truy cập camera</li>
                <li><strong>HTTPS:</strong> Camera chỉ hoạt động trên HTTPS hoặc localhost</li>
            </ul>
            <button class="btn btn-primary btn-sm mt-2" onclick="startCamera()">
                <i class="fas fa-redo me-1"></i>Thử lại
            </button>
        </div>
    `;

    $('.camera-controls').append(instructionsHtml);
}

function captureImage() {
    if (isProcessing) return;
    
    const video = document.getElementById('cameraVideo');
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    
    // Set canvas size
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    
    // Draw video frame to canvas
    context.drawImage(video, 0, 0);
    
    // Convert to base64
    const imageData = canvas.toDataURL('image/jpeg', 0.8);
    
    // Send to server
    sendCameraImage(imageData);
}

function sendCameraImage(imageData) {
    if (isProcessing) return;
    
    isProcessing = true;
    showLoading(true);
    
    $.ajax({
        url: '/api/predict',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            image_data: imageData
        }),
        success: function(response) {
            displayResults(response);

            // Auto-play audio after showing results
            if (response.audio_url) {
                setTimeout(() => {
                    playAudio(response.audio_url);
                }, 800); // Small delay to let UI update first
            }

            showAlert('Chụp ảnh và phân tích thành công!', 'success');
        },
        error: function(xhr, status, error) {
            console.error('Camera capture error:', error);
            const errorMsg = xhr.responseJSON ? xhr.responseJSON.error : 'Có lỗi xảy ra khi phân tích ảnh';
            showAlert(errorMsg, 'error');
        },
        complete: function() {
            isProcessing = false;
            showLoading(false);
        }
    });
}

function stopCamera() {
    if (currentStream) {
        currentStream.getTracks().forEach(track => track.stop());
        currentStream = null;
    }
}

// Results Display
function displayResults(data) {
    const resultsContainer = $('#resultsContainer');
    
    if (!resultsContainer.length) {
        // Create results container if it doesn't exist
        $('main').append('<div id="resultsContainer" class="container mt-4"></div>');
    }
    
    const html = `
        <div class="result-container fade-in">
            <div class="row">
                <div class="col-md-6">
                    <h4><i class="fas fa-image me-2"></i>Ảnh đã phân tích</h4>
                    <img src="${data.image_url}" alt="Analyzed image" class="result-image">
                </div>
                <div class="col-md-6">
                    <h4><i class="fas fa-brain me-2"></i>Kết quả phân loại</h4>
                    <div class="predictions-list">
                        ${data.predictions.map((pred, index) => {
                            // Get group color
                            const groupColors = {
                                'Rác tái chế khô': 'success',
                                'Rác hữu cơ': 'warning',
                                'Rác đặc biệt': 'danger',
                                'Rác không tái chế': 'secondary'
                            };
                            const groupColor = groupColors[pred.waste_group] || 'primary';

                            return `
                            <div class="prediction-item d-flex align-items-center">
                                <div class="prediction-rank">${pred.rank}</div>
                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>${pred.class_vietnamese || pred.class}</strong>
                                            <br>
                                            <span class="badge bg-${groupColor} mt-1">${pred.waste_group}</span>
                                        </div>
                                        <span class="badge bg-success">${pred.percentage.toFixed(1)}%</span>
                                    </div>
                                    <small class="text-muted mt-2 d-block">
                                        Hãy bỏ vào ${pred.disposal_instruction}
                                    </small>
                                    <div class="confidence-bar mt-2">
                                        <div class="confidence-fill" style="width: ${pred.percentage}%"></div>
                                    </div>
                                </div>
                            </div>
                            `;
                        }).join('')}
                    </div>
                    
                    ${data.audio_url ? `
                        <div class="audio-controls">
                            <button class="audio-btn" onclick="playAudio('${data.audio_url}')">
                                <i class="fas fa-volume-up me-2"></i>
                                Nghe hướng dẫn
                            </button>
                        </div>
                    ` : ''}
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            Phân tích lúc: ${new Date(data.timestamp).toLocaleString('vi-VN')}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    $('#resultsContainer').html(html);
    
    // Scroll to results
    $('html, body').animate({
        scrollTop: $('#resultsContainer').offset().top - 100
    }, 500);
}

// Audio Functions
function playAudio(audioUrl) {
    if (audioUrl.endsWith('.json')) {
        // Handle Web Speech API or fallback text
        fetch(audioUrl)
            .then(response => response.json())
            .then(data => {
                if (data.type === 'web_speech') {
                    // Use Web Speech API
                    speakText(data.text, data.lang, data.rate, data.pitch, data.volume);
                } else {
                    // Fallback to text display
                    showAlert(data.text, 'info');
                }
            })
            .catch(error => {
                console.error('Error loading audio data:', error);
                showAlert('Không thể tải dữ liệu âm thanh', 'error');
            });
    } else {
        // Play audio file
        const audio = new Audio(audioUrl);
        audio.play().catch(error => {
            console.error('Error playing audio:', error);
            showAlert('Không thể phát âm thanh', 'error');
        });
    }
}

function speakText(text, lang = 'vi-VN', rate = 0.9, pitch = 1.0, volume = 1.0) {
    // Check if Web Speech API is supported
    if (!('speechSynthesis' in window)) {
        console.warn('Web Speech API not supported');
        showAlert(text, 'info');
        return;
    }

    try {
        // Stop any ongoing speech
        speechSynthesis.cancel();

        // Create speech utterance
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = lang;
        utterance.rate = rate;
        utterance.pitch = pitch;
        utterance.volume = volume;

        // Try to find Vietnamese voice
        const voices = speechSynthesis.getVoices();
        const vietnameseVoice = voices.find(voice =>
            voice.lang.includes('vi') ||
            voice.name.toLowerCase().includes('vietnam') ||
            voice.name.toLowerCase().includes('vietnamese')
        );

        if (vietnameseVoice) {
            utterance.voice = vietnameseVoice;
            console.log('Using Vietnamese voice:', vietnameseVoice.name);
        } else {
            console.warn('No Vietnamese voice found, using default');
        }

        // Event handlers
        utterance.onstart = function() {
            console.log('Speech started');
            showAlert('🔊 Đang phát âm thanh...', 'info');
        };

        utterance.onend = function() {
            console.log('Speech ended');
        };

        utterance.onerror = function(event) {
            console.error('Speech error:', event.error);
            showAlert('Lỗi phát âm thanh: ' + event.error, 'error');
        };

        // Speak the text
        speechSynthesis.speak(utterance);

    } catch (error) {
        console.error('Error in speech synthesis:', error);
        showAlert(text, 'info');
    }
}

// Load voices when available
if ('speechSynthesis' in window) {
    speechSynthesis.onvoiceschanged = function() {
        const voices = speechSynthesis.getVoices();
        console.log('Available voices:', voices.length);

        // Log Vietnamese voices
        const vietnameseVoices = voices.filter(voice =>
            voice.lang.includes('vi') ||
            voice.name.toLowerCase().includes('vietnam')
        );

        if (vietnameseVoices.length > 0) {
            console.log('Vietnamese voices found:', vietnameseVoices.map(v => v.name));
        } else {
            console.warn('No Vietnamese voices available');
        }
    };
}

// Utility Functions
function showLoading(show) {
    if (show) {
        $('#loadingOverlay').fadeIn();
    } else {
        $('#loadingOverlay').fadeOut();
    }
}

function showAlert(message, type = 'info') {
    const alertClass = type === 'error' ? 'alert-danger' : 
                     type === 'success' ? 'alert-success' : 
                     type === 'warning' ? 'alert-warning' : 'alert-info';
    
    const icon = type === 'error' ? 'fas fa-exclamation-triangle' :
                type === 'success' ? 'fas fa-check-circle' :
                type === 'warning' ? 'fas fa-exclamation-circle' : 'fas fa-info-circle';
    
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // Remove existing alerts
    $('.alert').remove();
    
    // Add new alert at top of main content
    $('main').prepend(alertHtml);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);
}

// Cleanup when page unloads
$(window).on('beforeunload', function() {
    stopCamera();
});
