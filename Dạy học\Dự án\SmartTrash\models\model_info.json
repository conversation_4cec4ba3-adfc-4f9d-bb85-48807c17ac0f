{"model_name": "SmartTrash Classifier", "architecture": "MobileNetV2 + Custom Head", "input_size": [224, 224, 3], "num_classes": 10, "class_names": ["battery", "biological", "brown-glass", "cardboard", "green-glass", "metal", "paper", "plastic", "trash", "white-glass"], "test_accuracy": 0.909254252910614, "preprocessing": {"rescale": "1./255", "target_size": [224, 224]}, "training_info": {"batch_size": 32, "epochs_trained": 30, "optimizer": "<PERSON>", "loss": "categorical_crossentropy"}}