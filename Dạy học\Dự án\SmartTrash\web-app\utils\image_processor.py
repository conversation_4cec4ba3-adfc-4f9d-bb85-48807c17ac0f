#!/usr/bin/env python3
"""
Image Processor for SmartTrash
Xử lý ảnh từ camera và file upload
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import os

class ImageProcessor:
    def __init__(self):
        """Initialize image processor"""
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.gif']
        print("✅ Image processor initialized")
    
    def validate_image(self, image_path):
        """
        Validate if image file is valid
        
        Args:
            image_path: Path to image file
        
        Returns:
            bool: True if valid, False otherwise
        """
        try:
            # Check file extension
            _, ext = os.path.splitext(image_path.lower())
            if ext not in self.supported_formats:
                return False
            
            # Try to open image
            with Image.open(image_path) as img:
                img.verify()
            
            return True
            
        except Exception:
            return False
    
    def get_image_info(self, image_path):
        """
        Get image information
        
        Args:
            image_path: Path to image file
        
        Returns:
            dict: Image information
        """
        try:
            with Image.open(image_path) as img:
                return {
                    'width': img.width,
                    'height': img.height,
                    'mode': img.mode,
                    'format': img.format,
                    'size_bytes': os.path.getsize(image_path)
                }
        except Exception as e:
            return {'error': str(e)}
    
    def resize_image(self, image_path, target_size=(224, 224), save_path=None):
        """
        Resize image to target size
        
        Args:
            image_path: Path to input image
            target_size: Target size (width, height)
            save_path: Path to save resized image (optional)
        
        Returns:
            PIL Image object or saved path
        """
        try:
            with Image.open(image_path) as img:
                # Convert to RGB if needed
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Resize with high quality
                resized_img = img.resize(target_size, Image.Resampling.LANCZOS)
                
                if save_path:
                    resized_img.save(save_path, quality=95)
                    return save_path
                else:
                    return resized_img
                    
        except Exception as e:
            raise Exception(f"Error resizing image: {e}")
    
    def enhance_image(self, image_path, save_path=None):
        """
        Enhance image quality for better prediction
        
        Args:
            image_path: Path to input image
            save_path: Path to save enhanced image (optional)
        
        Returns:
            PIL Image object or saved path
        """
        try:
            with Image.open(image_path) as img:
                # Convert to RGB
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Enhance contrast
                enhancer = ImageEnhance.Contrast(img)
                img = enhancer.enhance(1.2)
                
                # Enhance sharpness
                enhancer = ImageEnhance.Sharpness(img)
                img = enhancer.enhance(1.1)
                
                # Enhance color
                enhancer = ImageEnhance.Color(img)
                img = enhancer.enhance(1.1)
                
                if save_path:
                    img.save(save_path, quality=95)
                    return save_path
                else:
                    return img
                    
        except Exception as e:
            raise Exception(f"Error enhancing image: {e}")
    
    def crop_center(self, image_path, crop_size=(224, 224), save_path=None):
        """
        Crop image from center
        
        Args:
            image_path: Path to input image
            crop_size: Size to crop (width, height)
            save_path: Path to save cropped image (optional)
        
        Returns:
            PIL Image object or saved path
        """
        try:
            with Image.open(image_path) as img:
                # Get center coordinates
                width, height = img.size
                crop_width, crop_height = crop_size
                
                left = (width - crop_width) // 2
                top = (height - crop_height) // 2
                right = left + crop_width
                bottom = top + crop_height
                
                # Crop image
                cropped_img = img.crop((left, top, right, bottom))
                
                if save_path:
                    cropped_img.save(save_path, quality=95)
                    return save_path
                else:
                    return cropped_img
                    
        except Exception as e:
            raise Exception(f"Error cropping image: {e}")
    
    def preprocess_for_model(self, image_path, target_size=(224, 224), enhance=True):
        """
        Complete preprocessing pipeline for model input
        
        Args:
            image_path: Path to input image
            target_size: Target size for model
            enhance: Whether to enhance image quality
        
        Returns:
            numpy array ready for model prediction
        """
        try:
            with Image.open(image_path) as img:
                # Convert to RGB
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Enhance if requested
                if enhance:
                    # Enhance contrast
                    enhancer = ImageEnhance.Contrast(img)
                    img = enhancer.enhance(1.1)
                    
                    # Enhance sharpness
                    enhancer = ImageEnhance.Sharpness(img)
                    img = enhancer.enhance(1.05)
                
                # Resize
                img = img.resize(target_size, Image.Resampling.LANCZOS)
                
                # Convert to numpy array
                img_array = np.array(img, dtype=np.float32)
                
                # Normalize to [0, 1]
                img_array = img_array / 255.0
                
                # Add batch dimension
                img_array = np.expand_dims(img_array, axis=0)
                
                return img_array
                
        except Exception as e:
            raise Exception(f"Error preprocessing image: {e}")
    
    def create_thumbnail(self, image_path, thumb_size=(150, 150), save_path=None):
        """
        Create thumbnail of image
        
        Args:
            image_path: Path to input image
            thumb_size: Thumbnail size (width, height)
            save_path: Path to save thumbnail (optional)
        
        Returns:
            PIL Image object or saved path
        """
        try:
            with Image.open(image_path) as img:
                # Create thumbnail
                img.thumbnail(thumb_size, Image.Resampling.LANCZOS)
                
                if save_path:
                    img.save(save_path, quality=85)
                    return save_path
                else:
                    return img
                    
        except Exception as e:
            raise Exception(f"Error creating thumbnail: {e}")
    
    def detect_objects_opencv(self, image_path):
        """
        Simple object detection using OpenCV (optional feature)
        
        Args:
            image_path: Path to input image
        
        Returns:
            dict: Detection results
        """
        try:
            # Read image
            img = cv2.imread(image_path)
            if img is None:
                raise Exception("Could not read image")
            
            # Convert to grayscale
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # Simple edge detection
            edges = cv2.Canny(gray, 50, 150)
            
            # Find contours
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Filter contours by area
            min_area = 100
            objects = []
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > min_area:
                    x, y, w, h = cv2.boundingRect(contour)
                    objects.append({
                        'x': int(x),
                        'y': int(y),
                        'width': int(w),
                        'height': int(h),
                        'area': int(area)
                    })
            
            return {
                'objects_detected': len(objects),
                'objects': objects,
                'image_size': {'width': img.shape[1], 'height': img.shape[0]}
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def batch_process(self, image_paths, target_size=(224, 224), enhance=True):
        """
        Process multiple images at once
        
        Args:
            image_paths: List of image paths
            target_size: Target size for processing
            enhance: Whether to enhance images
        
        Returns:
            List of processed image arrays
        """
        processed_images = []
        
        for image_path in image_paths:
            try:
                img_array = self.preprocess_for_model(image_path, target_size, enhance)
                processed_images.append(img_array)
            except Exception as e:
                print(f"Error processing {image_path}: {e}")
                processed_images.append(None)
        
        return processed_images
