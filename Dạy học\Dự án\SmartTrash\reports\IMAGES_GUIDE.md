# 📸 HƯỚNG DẪN HÌNH ẢNH CHO BÁO CÁO SMARTTRASH

## 🎯 MỤC ĐÍCH
Tài liệu này hướng dẫn tạo và sử dụng hình ảnh minh họa cho báo cáo dự án SmartTrash.

## 📊 DANH SÁCH HÌNH ẢNH CẦN TẠO

### 1. BIỂU ĐỒ DATASET (Data Visualization)

#### 1.1 Dataset Distribution Chart
- **Tên file**: `dataset_distribution.png`
- **Loại**: Bar chart
- **Nội dung**: Số lượng ảnh theo từng class
- **Dữ liệu**:
  ```
  Battery: 1,013 ảnh
  Biological: 1,013 ảnh
  Brown Glass: 1,013 ảnh
  Cardboard: 1,013 ảnh
  Green Glass: 1,013 ảnh
  Metal: 1,013 ảnh
  Paper: 1,013 ảnh
  Plastic: 1,013 ảnh
  Trash: 1,013 ảnh
  White Glass: 1,013 ảnh
  ```

#### 1.2 Train/Val/Test Split
- **Tên file**: `data_split.png`
- **Loại**: Pie chart
- **Nội dung**: <PERSON>ân chia dữ liệu
- **Dữ liệu**:
  ```
  Train: 7,975 ảnh (78.7%)
  Validation: 1,050 ảnh (10.4%)
  Test: 1,113 ảnh (11.0%)
  ```

#### 1.3 Waste Groups Distribution
- **Tên file**: `waste_groups.png`
- **Loại**: Grouped bar chart
- **Nội dung**: Phân bố theo 4 nhóm rác
- **Dữ liệu**:
  ```
  Rác tái chế khô: 7,091 ảnh (7 classes)
  Rác hữu cơ: 1,013 ảnh (1 class)
  Rác đặc biệt: 1,013 ảnh (1 class)
  Rác không tái chế: 1,013 ảnh (1 class)
  ```

### 2. KIẾN TRÚC HỆ THỐNG (System Architecture)

#### 2.1 Overall System Architecture
- **Tên file**: `system_architecture.png`
- **Loại**: Flowchart/Diagram
- **Nội dung**: Luồng xử lý từ input đến output
- **Thành phần**:
  ```
  User Input (Camera/Upload) → 
  Image Processing → 
  AI Model (MobileNetV2) → 
  Classification Results → 
  Audio Feedback (Vietnamese)
  ```

#### 2.2 Model Architecture
- **Tên file**: `model_architecture.png`
- **Loại**: Neural network diagram
- **Nội dung**: Cấu trúc MobileNetV2 + Custom Head
- **Layers**:
  ```
  Input (224x224x3) →
  MobileNetV2 Base →
  GlobalAveragePooling2D →
  Dense(512) + Dropout →
  Dense(10, softmax)
  ```

#### 2.3 Web Application Flow
- **Tên file**: `webapp_flow.png`
- **Loại**: User journey diagram
- **Nội dung**: Luồng sử dụng web app
- **Steps**:
  ```
  Homepage → 
  Choose Input Method → 
  Camera/Upload → 
  Processing → 
  Results Display → 
  Audio Feedback
  ```

### 3. KẾT QUẢ TRAINING (Training Results)

#### 3.1 Training History
- **Tên file**: `training_history.png`
- **Loại**: Line charts (2x2 grid)
- **Nội dung**: 
  - Training/Validation Accuracy
  - Training/Validation Loss
  - Learning Rate Schedule
  - Top-3 Accuracy

#### 3.2 Confusion Matrix
- **Tên file**: `confusion_matrix.png`
- **Loại**: Heatmap
- **Nội dung**: Ma trận nhầm lẫn 10x10 classes
- **Format**: Seaborn heatmap với annotations

#### 3.3 Classification Report
- **Tên file**: `classification_report.png`
- **Loại**: Table/Heatmap
- **Nội dung**: Precision, Recall, F1-score cho từng class

### 4. GIAO DIỆN NGƯỜI DÙNG (User Interface)

#### 4.1 Homepage Screenshot
- **Tên file**: `homepage_ui.png`
- **Loại**: Screenshot
- **Nội dung**: Trang chủ với 10 loại rác và 4 nhóm

#### 4.2 Camera Interface
- **Tên file**: `camera_ui.png`
- **Loại**: Screenshot
- **Nội dung**: Giao diện camera với preview

#### 4.3 Upload Interface
- **Tên file**: `upload_ui.png`
- **Loại**: Screenshot
- **Nội dung**: Giao diện upload với drag & drop

#### 4.4 Results Display
- **Tên file**: `results_ui.png`
- **Loại**: Screenshot
- **Nội dung**: Hiển thị kết quả với top-3 predictions

### 5. SAMPLE IMAGES (Ảnh mẫu)

#### 5.1 Sample Images Grid
- **Tên file**: `sample_images_grid.png`
- **Loại**: Image grid (10x3)
- **Nội dung**: 3 ảnh mẫu từ mỗi class
- **Layout**: 10 rows x 3 columns

#### 5.2 Prediction Examples
- **Tên file**: `prediction_examples.png`
- **Loại**: Before/After comparison
- **Nội dung**: Ảnh input và kết quả prediction

### 6. PERFORMANCE METRICS (Hiệu suất)

#### 6.1 Accuracy Comparison
- **Tên file**: `accuracy_comparison.png`
- **Loại**: Bar chart
- **Nội dung**: So sánh accuracy giữa các classes

#### 6.2 Inference Time
- **Tên file**: `inference_time.png`
- **Loại**: Box plot hoặc histogram
- **Nội dung**: Phân bố thời gian inference

#### 6.3 Model Size Comparison
- **Tên file**: `model_comparison.png`
- **Loại**: Scatter plot
- **Nội dung**: Accuracy vs Model Size so với các models khác

### 7. WORKFLOW DIAGRAMS (Sơ đồ quy trình)

#### 7.1 Development Workflow
- **Tên file**: `development_workflow.png`
- **Loại**: Process diagram
- **Nội dung**: Quy trình phát triển từ data đến deployment

#### 7.2 Data Processing Pipeline
- **Tên file**: `data_pipeline.png`
- **Loại**: Flowchart
- **Nội dung**: Quy trình xử lý dữ liệu

#### 7.3 Deployment Architecture
- **Tên file**: `deployment_architecture.png`
- **Loại**: Infrastructure diagram
- **Nội dung**: Kiến trúc triển khai production

## 🛠️ CÔNG CỤ TẠO HÌNH ẢNH

### Python Libraries
```python
# Data visualization
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px

# Image processing
from PIL import Image
import cv2

# Model visualization
import tensorflow as tf
from tensorflow.keras.utils import plot_model
```

### Online Tools
- **Diagrams**: Draw.io, Lucidchart
- **Screenshots**: Browser dev tools
- **Image editing**: GIMP, Photoshop
- **Charts**: Google Charts, Chart.js

## 📝 SCRIPT TẠO HÌNH ẢNH

### Dataset Visualization Script
```python
# File: create_dataset_charts.py
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

# Dataset distribution
classes = ['Battery', 'Biological', 'Brown Glass', 'Cardboard', 
           'Green Glass', 'Metal', 'Paper', 'Plastic', 'Trash', 'White Glass']
counts = [1013] * 10

plt.figure(figsize=(12, 6))
bars = plt.bar(classes, counts, color='skyblue')
plt.title('Dataset Distribution by Class')
plt.xlabel('Waste Classes')
plt.ylabel('Number of Images')
plt.xticks(rotation=45)
plt.tight_layout()
plt.savefig('reports/images/dataset_distribution.png', dpi=300)
plt.show()
```

### Training History Script
```python
# File: create_training_charts.py
# Load training history from model training
history = load_training_history()

fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# Accuracy
axes[0,0].plot(history['accuracy'], label='Training')
axes[0,0].plot(history['val_accuracy'], label='Validation')
axes[0,0].set_title('Model Accuracy')
axes[0,0].legend()

# Loss
axes[0,1].plot(history['loss'], label='Training')
axes[0,1].plot(history['val_loss'], label='Validation')
axes[0,1].set_title('Model Loss')
axes[0,1].legend()

plt.tight_layout()
plt.savefig('reports/images/training_history.png', dpi=300)
```

## 📋 CHECKLIST HÌNH ẢNH

### Cần tạo từ code:
- [ ] Dataset distribution charts
- [ ] Training history plots
- [ ] Confusion matrix
- [ ] Model architecture diagram
- [ ] Performance metrics

### Cần chụp screenshot:
- [ ] Homepage UI
- [ ] Camera interface
- [ ] Upload interface
- [ ] Results display
- [ ] Mobile responsive

### Cần vẽ diagram:
- [ ] System architecture
- [ ] Data flow
- [ ] Deployment diagram
- [ ] User journey

### Cần tạo composite:
- [ ] Sample images grid
- [ ] Before/after predictions
- [ ] Feature comparison
- [ ] Technology stack

## 🎨 STYLE GUIDE

### Colors
- **Primary**: #28a745 (Green)
- **Secondary**: #6c757d (Gray)
- **Success**: #28a745
- **Warning**: #ffc107
- **Danger**: #dc3545
- **Info**: #17a2b8

### Fonts
- **Title**: Arial Bold, 16-20pt
- **Body**: Arial Regular, 12-14pt
- **Code**: Courier New, 10-12pt

### Layout
- **DPI**: 300 for print, 150 for web
- **Format**: PNG for charts, JPG for photos
- **Size**: Max 1920x1080 for presentations

---

**Sử dụng hướng dẫn này để tạo đầy đủ hình ảnh minh họa cho báo cáo dự án SmartTrash.**
