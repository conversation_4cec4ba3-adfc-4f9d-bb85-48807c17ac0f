/* SmartTrash Custom Styles */

:root {
    --primary-color: #28a745;
    --secondary-color: #20c997;
    --accent-color: #007bff;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-content {
    text-align: center;
    color: white;
}

/* Camera Styles */
.camera-container {
    position: relative;
    max-width: 640px;
    margin: 0 auto;
}

.camera-video {
    width: 100%;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.camera-controls {
    margin-top: 20px;
    text-align: center;
}

.capture-btn {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 4px solid white;
    background: var(--primary-color);
    color: white;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.capture-btn:hover {
    background: var(--secondary-color);
    transform: scale(1.1);
}

/* Upload Styles */
.upload-area {
    border: 3px dashed #ccc;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background-color: rgba(40, 167, 69, 0.1);
}

.upload-area.dragover {
    border-color: var(--primary-color);
    background-color: rgba(40, 167, 69, 0.2);
}

.upload-icon {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 20px;
}

/* Results Styles */
.result-container {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    margin-top: 30px;
}

.result-image {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.prediction-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.prediction-item:hover {
    transform: translateX(5px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.prediction-rank {
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 15px;
}

.confidence-bar {
    background: #e9ecef;
    border-radius: 10px;
    height: 8px;
    overflow: hidden;
    margin-top: 5px;
}

.confidence-fill {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    height: 100%;
    border-radius: 10px;
    transition: width 0.5s ease;
}

/* Audio Controls */
.audio-controls {
    text-align: center;
    margin-top: 20px;
}

.audio-btn {
    background: var(--accent-color);
    color: white;
    border: none;
    border-radius: 50px;
    padding: 12px 24px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.audio-btn:hover {
    background: #0056b3;
    transform: translateY(-2px);
}

/* Waste Type Cards */
.waste-type-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.waste-type-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 2px solid #e9ecef;
}

.waste-type-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    border-color: var(--primary-color);
}

.waste-sample-image {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.waste-icon {
    font-size: 48px;
    margin-bottom: 15px;
}

/* Waste Group Cards */
.waste-group-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.waste-group-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.group-icon {
    margin-bottom: 20px;
}

.group-items .badge {
    font-size: 0.75rem;
    padding: 0.4em 0.6em;
}

/* Responsive Design */
@media (max-width: 768px) {
    .camera-container {
        padding: 0 15px;
    }
    
    .result-container {
        padding: 20px;
        margin: 20px 15px;
    }
    
    .prediction-item {
        padding: 12px;
    }
    
    .capture-btn {
        width: 60px;
        height: 60px;
        font-size: 20px;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Success/Error Messages */
.alert-custom {
    border-radius: 10px;
    border: none;
    padding: 15px 20px;
    margin-bottom: 20px;
}

.alert-success-custom {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
    border-left: 4px solid #28a745;
}

.alert-error-custom {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
    border-left: 4px solid #dc3545;
}

/* Button Styles */
.btn-custom {
    border-radius: 25px;
    padding: 12px 30px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
}

.btn-custom:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

/* Card Hover Effects */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Progress Bar */
.progress-custom {
    height: 8px;
    border-radius: 10px;
    background: #e9ecef;
    overflow: hidden;
}

.progress-bar-custom {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 10px;
    transition: width 0.5s ease;
}
