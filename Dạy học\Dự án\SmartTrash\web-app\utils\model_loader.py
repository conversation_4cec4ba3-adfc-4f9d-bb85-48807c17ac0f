#!/usr/bin/env python3
"""
Model Loader for SmartTrash
Load và sử dụng trained model để prediction
"""

import tensorflow as tf
import numpy as np
from PIL import Image
import json
import os

class SmartTrashModel:
    def __init__(self, model_path, labels_path, model_info_path=None):
        """
        Initialize SmartTrash model
        
        Args:
            model_path: Đường dẫn đến file model.h5
            labels_path: Đường dẫn đến file labels.txt
            model_info_path: Đường dẫn đến file model_info.json (optional)
        """
        self.model_path = model_path
        self.labels_path = labels_path
        self.model_info_path = model_info_path
        
        # Load model components
        self.model = self._load_model()
        self.class_names = self._load_labels()
        self.model_info = self._load_model_info()
        
        print(f"✅ SmartTrash model loaded successfully!")
        print(f"📋 Classes: {len(self.class_names)}")
    
    def _load_model(self):
        """Load trained TensorFlow model"""
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"Model file not found: {self.model_path}")
        
        try:
            model = tf.keras.models.load_model(self.model_path)
            print(f"📦 Model loaded from: {self.model_path}")
            return model
        except Exception as e:
            raise Exception(f"Error loading model: {e}")
    
    def _load_labels(self):
        """Load class labels"""
        if not os.path.exists(self.labels_path):
            raise FileNotFoundError(f"Labels file not found: {self.labels_path}")
        
        with open(self.labels_path, 'r', encoding='utf-8') as f:
            labels = [line.strip() for line in f.readlines() if line.strip()]
        
        print(f"🏷️ Labels loaded: {len(labels)} classes")
        return labels
    
    def _load_model_info(self):
        """Load model information"""
        if not self.model_info_path or not os.path.exists(self.model_info_path):
            print("⚠️ Model info file not found, using defaults")
            return {
                'input_size': [224, 224, 3],
                'preprocessing': {'rescale': '1./255'}
            }
        
        try:
            with open(self.model_info_path, 'r', encoding='utf-8') as f:
                info = json.load(f)
            print(f"ℹ️ Model info loaded")
            return info
        except Exception as e:
            print(f"⚠️ Error loading model info: {e}")
            return {
                'input_size': [224, 224, 3],
                'preprocessing': {'rescale': '1./255'}
            }
    
    def preprocess_image(self, image_path):
        """
        Preprocess image for prediction
        
        Args:
            image_path: Đường dẫn đến ảnh
        
        Returns:
            Preprocessed image array
        """
        try:
            # Get target size from model info
            target_size = tuple(self.model_info['input_size'][:2])  # (width, height)
            
            # Load and convert image
            img = Image.open(image_path)
            
            # Convert to RGB if needed
            if img.mode != 'RGB':
                img = img.convert('RGB')
            
            # Resize image
            img = img.resize(target_size)
            
            # Convert to array and normalize
            img_array = np.array(img, dtype=np.float32)
            img_array = img_array / 255.0  # Normalize to [0,1]
            
            # Add batch dimension
            img_array = np.expand_dims(img_array, axis=0)
            
            return img_array
            
        except Exception as e:
            raise Exception(f"Error preprocessing image {image_path}: {e}")
    
    def predict(self, image_path, top_k=3):
        """
        Predict image class
        
        Args:
            image_path: Đường dẫn đến ảnh
            top_k: Số lượng predictions hàng đầu
        
        Returns:
            Dictionary với predictions
        """
        try:
            # Preprocess image
            img_array = self.preprocess_image(image_path)
            
            # Make prediction
            predictions = self.model.predict(img_array, verbose=0)
            
            # Get top-k predictions
            top_indices = np.argsort(predictions[0])[::-1][:top_k]
            
            results = {
                'image_path': image_path,
                'predictions': [],
                'model_info': {
                    'model_name': self.model_info.get('model_name', 'SmartTrash Classifier'),
                    'architecture': self.model_info.get('architecture', 'MobileNetV2')
                }
            }
            
            for i, idx in enumerate(top_indices):
                confidence = float(predictions[0][idx])
                class_name = self.class_names[idx]
                results['predictions'].append({
                    'rank': i + 1,
                    'class': class_name,
                    'class_vietnamese': self._get_vietnamese_name(class_name),
                    'waste_group': self._get_waste_group(class_name),
                    'confidence': confidence,
                    'percentage': confidence * 100,
                    'disposal_instruction': self._get_disposal_instruction(class_name)
                })
            
            return results
            
        except Exception as e:
            raise Exception(f"Error making prediction: {e}")
    
    def _get_vietnamese_name(self, class_name):
        """Get Vietnamese name for class"""
        vietnamese_names = {
            'battery': 'Pin/Ắc quy',
            'biological': 'Rác hữu cơ',
            'brown-glass': 'Thủy tinh nâu',
            'cardboard': 'Bìa carton',
            'green-glass': 'Thủy tinh xanh',
            'metal': 'Kim loại',
            'paper': 'Giấy',
            'plastic': 'Nhựa',
            'trash': 'Rác thông thường',
            'white-glass': 'Thủy tinh trắng'
        }
        return vietnamese_names.get(class_name, class_name)

    def _get_waste_group(self, class_name):
        """Get waste group for class"""
        waste_groups = {
            # Rác tái chế khô
            'paper': 'Rác tái chế khô',
            'cardboard': 'Rác tái chế khô',
            'plastic': 'Rác tái chế khô',
            'metal': 'Rác tái chế khô',
            'white-glass': 'Rác tái chế khô',
            'green-glass': 'Rác tái chế khô',
            'brown-glass': 'Rác tái chế khô',

            # Rác hữu cơ
            'biological': 'Rác hữu cơ',

            # Rác đặc biệt
            'battery': 'Rác đặc biệt',

            # Rác không tái chế
            'trash': 'Rác không tái chế'
        }
        return waste_groups.get(class_name, 'Không xác định')

    def _get_disposal_instruction(self, class_name):
        """Get disposal instruction for class"""
        group = self._get_waste_group(class_name)

        instructions = {
            'Rác tái chế khô': 'thùng chứa rác tái chế khô',
            'Rác hữu cơ': 'thùng chứa rác hữu cơ',
            'Rác đặc biệt': 'thùng chứa rác đặc biệt',
            'Rác không tái chế': 'thùng chứa rác không tái chế'
        }

        return instructions.get(group, 'thùng rác phù hợp')
    
    def get_model_info(self):
        """Get model information"""
        return {
            'model_name': self.model_info.get('model_name', 'SmartTrash Classifier'),
            'architecture': self.model_info.get('architecture', 'MobileNetV2'),
            'num_classes': len(self.class_names),
            'classes': self.class_names,
            'input_size': self.model_info.get('input_size', [224, 224, 3]),
            'test_accuracy': self.model_info.get('test_accuracy', 'N/A')
        }
    
    def predict_batch(self, image_paths, top_k=3):
        """
        Predict multiple images at once
        
        Args:
            image_paths: List of image paths
            top_k: Number of top predictions
        
        Returns:
            List of prediction results
        """
        results = []
        for image_path in image_paths:
            try:
                result = self.predict(image_path, top_k)
                results.append(result)
            except Exception as e:
                results.append({
                    'image_path': image_path,
                    'error': str(e)
                })
        
        return results
