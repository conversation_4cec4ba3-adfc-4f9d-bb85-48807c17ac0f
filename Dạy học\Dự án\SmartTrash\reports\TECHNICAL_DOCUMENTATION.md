# 🔧 TÀI LIỆU KỸ THUẬT - SMARTTRASH

## 🏗️ KIẾN TRÚC HỆ THỐNG

### Tổng quan Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   AI Model      │
│                 │    │                 │    │                 │
│ • HTML5/CSS3    │◄──►│ • Flask API     │◄──►│ • TensorFlow    │
│ • JavaScript    │    │ • Image Proc.   │    │ • MobileNetV2   │
│ • <PERSON><PERSON><PERSON>     │    │ • Audio Gen.    │    │ • Transfer      │
│ • Web Speech    │    │ • File Upload   │    │   Learning      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Data Flow
```
Camera/Upload → Image Processing → Model Prediction → Result Display → Audio Feedback
     ↓               ↓                    ↓              ↓              ↓
  Capture         Resize/             Top-3 Classes   Visual UI    Vietnamese
  Image          Normalize            + Confidence    + Groups     Speech
```

## 🧠 CHI TIẾT MODEL

### MobileNetV2 Architecture
```python
# Base Model
base_model = MobileNetV2(
    weights='imagenet',
    include_top=False,
    input_shape=(224, 224, 3)
)

# Custom Classification Head
model = Sequential([
    base_model,
    GlobalAveragePooling2D(),
    Dropout(0.3),
    Dense(512, activation='relu'),
    BatchNormalization(),
    Dropout(0.5),
    Den<PERSON>(10, activation='softmax')  # 10 classes
])
```

### Training Configuration
```python
# Optimizer
optimizer = Adam(learning_rate=0.001)

# Loss & Metrics
loss = 'categorical_crossentropy'
metrics = ['accuracy', 'top_3_accuracy']

# Data Augmentation
train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=20,
    width_shift_range=0.2,
    height_shift_range=0.2,
    shear_range=0.2,
    zoom_range=0.2,
    horizontal_flip=True,
    brightness_range=[0.8, 1.2]
)
```

### Model Performance
- **Parameters**: ~2.3M (trainable: ~1.2M)
- **FLOPs**: ~300M
- **Memory**: ~10MB
- **Inference**: <1s on CPU

## 🌐 WEB APPLICATION

### Flask API Endpoints
```python
# Main Routes
GET  /                 # Homepage
GET  /camera          # Camera page
GET  /upload          # Upload page

# API Endpoints
POST /api/predict     # Image prediction
GET  /api/model_info  # Model information
GET  /api/classes     # Supported classes
```

### Request/Response Format
```json
// POST /api/predict
{
  "image_data": "base64_encoded_image"
}

// Response
{
  "predictions": [
    {
      "rank": 1,
      "class": "plastic",
      "class_vietnamese": "Nhựa",
      "waste_group": "Rác tái chế khô",
      "confidence": 0.9026,
      "percentage": 90.26,
      "disposal_instruction": "thùng chứa rác tái chế khô"
    }
  ],
  "audio_url": "/static/audio/speech_plastic.json",
  "timestamp": "2025-06-19T11:20:00"
}
```

### Frontend Technologies
- **HTML5**: Semantic markup, camera API
- **CSS3**: Flexbox, Grid, animations
- **JavaScript ES6**: Async/await, modules
- **Bootstrap 5**: Responsive components
- **Web Speech API**: Text-to-speech

## 🔊 AUDIO SYSTEM

### Web Speech API Implementation
```javascript
function speakText(text, lang = 'vi-VN') {
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = lang;
    utterance.rate = 0.9;
    utterance.pitch = 1.0;
    utterance.volume = 1.0;
    
    // Find Vietnamese voice
    const voices = speechSynthesis.getVoices();
    const vietnameseVoice = voices.find(voice => 
        voice.lang.includes('vi')
    );
    
    if (vietnameseVoice) {
        utterance.voice = vietnameseVoice;
    }
    
    speechSynthesis.speak(utterance);
}
```

### Audio Text Generation
```python
def _generate_text(self, class_name):
    waste_group = self._get_waste_group(class_name)
    item_name = vietnamese_names.get(class_name, class_name)
    
    text = f"Đây là {item_name}, thuộc nhóm {waste_group}. " \
           f"Hãy bỏ vào thùng chứa {waste_group}."
    
    return text
```

## 📊 WASTE CLASSIFICATION SYSTEM

### Class Mapping
```python
WASTE_GROUPS = {
    # Rác tái chế khô (7 classes)
    'paper': 'Rác tái chế khô',
    'cardboard': 'Rác tái chế khô',
    'plastic': 'Rác tái chế khô',
    'metal': 'Rác tái chế khô',
    'white-glass': 'Rác tái chế khô',
    'green-glass': 'Rác tái chế khô',
    'brown-glass': 'Rác tái chế khô',
    
    # Rác hữu cơ (1 class)
    'biological': 'Rác hữu cơ',
    
    # Rác đặc biệt (1 class)
    'battery': 'Rác đặc biệt',
    
    # Rác không tái chế (1 class)
    'trash': 'Rác không tái chế'
}
```

### Group Colors & Icons
```css
.group-recyclable { color: #28a745; } /* Green */
.group-organic { color: #ffc107; }    /* Yellow */
.group-hazardous { color: #dc3545; }  /* Red */
.group-general { color: #6c757d; }    /* Gray */
```

## 🔧 DEPLOYMENT

### Local Development
```bash
# Setup environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt

# Run application
python app.py
```

### Production Deployment
```bash
# Using Gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app

# Using Docker
docker build -t smarttrash .
docker run -p 5000:5000 smarttrash
```

### Environment Variables
```bash
FLASK_ENV=production
SECRET_KEY=your_secret_key
MODEL_PATH=../models/smarttrash_model.h5
LABELS_PATH=../models/labels.txt
MAX_CONTENT_LENGTH=16777216  # 16MB
```

## 🧪 TESTING

### Unit Tests
```python
# Test model loading
def test_model_loading():
    model = SmartTrashModel(model_path, labels_path)
    assert model.model is not None
    assert len(model.class_names) == 10

# Test prediction
def test_prediction():
    result = model.predict(test_image_path)
    assert 'predictions' in result
    assert len(result['predictions']) == 3
```

### Integration Tests
```python
# Test API endpoints
def test_predict_endpoint():
    response = client.post('/api/predict', 
                          files={'image': test_image})
    assert response.status_code == 200
    assert 'predictions' in response.json
```

### Performance Tests
```python
# Test inference time
def test_inference_speed():
    start_time = time.time()
    result = model.predict(test_image)
    inference_time = time.time() - start_time
    assert inference_time < 1.0  # Less than 1 second
```

## 📈 MONITORING & ANALYTICS

### Metrics Collection
```python
# Performance metrics
inference_time = time.time() - start_time
accuracy = calculate_accuracy(predictions, ground_truth)
memory_usage = psutil.Process().memory_info().rss

# User metrics
prediction_count += 1
class_distribution[predicted_class] += 1
```

### Logging
```python
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('smarttrash.log'),
        logging.StreamHandler()
    ]
)
```

## 🔒 SECURITY

### Input Validation
```python
# File upload validation
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp'}
MAX_FILE_SIZE = 16 * 1024 * 1024  # 16MB

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS
```

### CSRF Protection
```python
from flask_wtf.csrf import CSRFProtect

csrf = CSRFProtect(app)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY')
```

### Rate Limiting
```python
from flask_limiter import Limiter

limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["100 per hour"]
)

@app.route('/api/predict', methods=['POST'])
@limiter.limit("10 per minute")
def predict():
    # Prediction logic
```

## 🚀 OPTIMIZATION

### Model Optimization
```python
# TensorFlow Lite conversion
converter = tf.lite.TFLiteConverter.from_keras_model(model)
converter.optimizations = [tf.lite.Optimize.DEFAULT]
tflite_model = converter.convert()
```

### Caching
```python
from functools import lru_cache

@lru_cache(maxsize=100)
def predict_cached(image_hash):
    return model.predict(image_path)
```

### CDN & Static Files
```nginx
# Nginx configuration
location /static/ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

---

**Tài liệu này cung cấp chi tiết kỹ thuật đầy đủ cho việc phát triển, triển khai và bảo trì hệ thống SmartTrash.**
