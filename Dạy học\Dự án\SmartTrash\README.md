# 🗑️ SmartTrash - <PERSON><PERSON> thống Phân loại Rác Thông minh

## 📋 Tổng quan
Hệ thống phân loại rác thông minh sử dụng AI và camera để nhận diện và phân loại 10 loại rác khác nhau thành 4 nh<PERSON>m ch<PERSON>h, kế<PERSON> hợ<PERSON> với phản hồi âm thanh tiếng Việt.

## 🎯 M<PERSON>c tiêu
- Phân loại rác tự động qua camera/upload
- Nhận diện 10 loại rác phổ biến
- Phân nhóm thành 4 loại thùng rác
- <PERSON><PERSON>n hồi âm thanh tiếng Việt hướng dẫn người dùng
- Giao diện web responsive, thân thiện

## 📊 Dataset
- **Tổng số ảnh**: 10,138 images
- **Phân chia**: Train (7,975) | Val (1,050) | Test (1,113)
- **Classes**: 10 loại rác → 4 nhóm

### 🗂️ 10 Loại rác đ<PERSON> nh<PERSON>n diện:
1. **🔋 Battery** - Pin/ắc quy
2. **🍃 Biological** - <PERSON><PERSON><PERSON> cơ
3. **🟤 Brown Glass** - Thủy tinh nâu
4. **📦 Cardboard** - Bìa carton
5. **🟢 Green Glass** - Thủy tinh xanh
6. **⚙️ Metal** - Kim loại
7. **📄 Paper** - Giấy
8. **🥤 Plastic** - Nhựa
9. **🗑️ Trash** - Rác thông thường
10. **⚪ White Glass** - Thủy tinh trắng

### 🏷️ 4 Nhóm phân loại:
| Nhóm | Loại rác | Màu thùng |
|------|----------|-----------|
| **🟢 Rác tái chế khô** | Giấy, bìa carton, nhựa, kim loại, thủy tinh | Xanh lá |
| **🟡 Rác hữu cơ** | Rác hữu cơ (biological) | Vàng |
| **🔴 Rác đặc biệt** | Pin (battery) | Đỏ |
| **⚫ Rác không tái chế** | Rác thông thường (trash) | Đen |

## 🏗️ Cấu trúc Dự án

```
SmartTrash/ (Clean & Organized)
├── 📁 rubbish-data/           # Dataset gốc (10,138 ảnh)
│   ├── train/                 # Dữ liệu training (7,975 ảnh)
│   ├── val/                   # Dữ liệu validation (1,050 ảnh)
│   └── test/                  # Dữ liệu test (1,113 ảnh)
├── 📁 training/               # Google Colab notebooks
│   └── README.md              # Hướng dẫn training
├── 📁 models/                 # Models đã train
│   ├── smarttrash_model.h5    # Model chính (TensorFlow)
│   ├── labels.txt             # Danh sách 10 classes
│   ├── model_info.json        # Metadata model
│   └── training_history.png   # Training plots
├── 📁 web-app/               # Web application Flask ⭐
│   ├── app.py                 # Flask main app
│   ├── requirements.txt       # Dependencies web app
│   ├── templates/             # HTML templates
│   ├── static/                # CSS, JS, uploads
│   └── utils/                 # Model loader, audio, image processing
├── 📁 scripts/               # Utility scripts
│   ├── create_report_charts.py # Tạo biểu đồ báo cáo
│   └── simple_chart_test.py    # Test chart creation
├── 📁 reports/               # Báo cáo và tài liệu
│   ├── PROJECT_SUMMARY.md     # Tóm tắt dự án
│   ├── TECHNICAL_DOCUMENTATION.md # Tài liệu kỹ thuật
│   ├── IMAGES_GUIDE.md        # Hướng dẫn tạo hình ảnh
│   └── images/                # Hình ảnh minh họa
├── README.md                  # File này
└── FINAL_SUMMARY.md          # Tóm tắt hoàn thành
```

## 🚀 Hướng dẫn Sử dụng

### Bước 1: Phân tích Dataset
```bash
# Chạy phân tích nhanh dataset
python quick_analysis.py

# Hoặc phân tích chi tiết (có biểu đồ)
python data_analysis.py
```

### Bước 2: Training Model trên Google Colab
1. Upload folder `rubbish-data` lên Google Drive
2. Mở file `SmartTrash_Training.ipynb` trên Google Colab
3. Chọn Runtime > Change runtime type > GPU
4. Chạy từng cell theo thứ tự
5. Download model package sau khi training xong

### Bước 3: Test Model
```bash
# Test model với ảnh mẫu
python test_model.py
```

## 🧠 Model Architecture
- **Base Model**: MobileNetV2 (Transfer Learning)
- **Input Size**: 224x224x3
- **Classes**: 10 categories
- **Expected Accuracy**: >90%

## 📈 Kết quả Dataset Analysis
```
📊 SMARTTRASH DATASET ANALYSIS SUMMARY
============================================================
📈 TỔNG QUAN:
   • Tổng số classes: 10
   • Tổng số ảnh: 10,138
   • Train: 7,975 ảnh (78.7%)
   • Validation: 1,050 ảnh (10.4%)
   • Test: 1,113 ảnh (11.0%)

🖼️  THÔNG TIN ẢNH:
   • Kích thước trung bình: 351 x 272 pixels
   • File size trung bình: 11.7 KB
   • Aspect ratio trung bình: 1.30
```

## 💡 Khuyến nghị Training
- **Image Size**: 224x224 pixels
- **Batch Size**: 32-64
- **Learning Rate**: 0.001
- **Data Augmentation**: rotation, flip, zoom, brightness
- **Architecture**: MobileNetV2 hoặc EfficientNet

## 🔧 Requirements
### Cho Training (Google Colab):
```
tensorflow==2.13.0
matplotlib>=3.5.0
seaborn>=0.11.0
pillow>=8.0.0
opencv-python>=4.5.0
scikit-learn>=1.0.0
```

### Cho Web App (sẽ tạo):
```
flask
tensorflow
opencv-python
pillow
numpy
gtts (cho text-to-speech)
```

## 📁 Files quan trọng

### Training Files:
- `SmartTrash_Training.ipynb` - Notebook training chính
- `requirements_colab.txt` - Dependencies cho Colab
- `README_training.md` - Hướng dẫn training chi tiết

### Analysis Files:
- `quick_analysis.py` - Phân tích dataset nhanh
- `data_analysis.py` - Phân tích dataset chi tiết
- `test_model.py` - Test model sau training

### Model Files (sau training):
- `smarttrash_model.h5` - Model chính
- `labels.txt` - Danh sách classes
- `model_info.json` - Metadata model

## 🎯 Roadmap

### ✅ Đã hoàn thành:
- [x] Phân tích dataset
- [x] Tạo Google Colab notebook training
- [x] Thiết kế model architecture
- [x] Script test model

### 🔄 Đang thực hiện:
- [ ] Training model trên Google Colab
- [ ] Tạo web application
- [ ] Tích hợp camera
- [ ] Tích hợp audio feedback

### 📋 Kế hoạch tiếp theo:
- [ ] Flask web app với camera integration
- [ ] Text-to-speech cho phản hồi âm thanh
- [ ] UI/UX improvements
- [ ] Testing và deployment

## 🚨 Troubleshooting
1. **Dataset not found**: Kiểm tra đường dẫn folder `rubbish-data`
2. **Model loading error**: Đảm bảo đã training và có file model.h5
3. **Memory error**: Giảm batch size hoặc image size

## 📞 Support
Nếu gặp vấn đề:
1. Kiểm tra cấu trúc folder đúng format
2. Đảm bảo có đủ dependencies
3. Xem log lỗi chi tiết

## 🎉 Next Steps
1. **Training**: Chạy notebook trên Google Colab
2. **Web App**: Tạo Flask application
3. **Integration**: Tích hợp camera và audio
4. **Testing**: Test với ảnh thực tế
5. **Deployment**: Deploy lên production
