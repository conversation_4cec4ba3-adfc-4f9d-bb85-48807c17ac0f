# 🌐 SmartTrash Web Application

## 📋 Tổng quan
Web application cho hệ thống phân loại rác thông minh sử dụng Flask và TensorFlow.

## 🚀 Cài đặt và Chạy

### 1. <PERSON><PERSON><PERSON> bị Model
Trước tiên, copy các file model đã training vào folder `../models/`:
```
models/
├── smarttrash_model.h5
├── labels.txt
└── model_info.json
```

### 2. Cài đặt Dependencies
```bash
# Tạo virtual environment (khuyến nghị)
python -m venv venv

# Kích hoạt virtual environment
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# Cài đặt packages
pip install -r requirements.txt
```

### 3. Chạy Application
```bash
python app.py
```

Mở trình duyệt và truy cập: http://localhost:5000

## 📁 Cấu trúc Project

```
web-app/
├── app.py                 # Flask main application
├── requirements.txt       # Dependencies
├── README.md             # Hướng dẫn này
├── static/               # Static files
│   ├── css/
│   │   └── style.css     # Custom styles
│   ├── js/
│   │   └── main.js       # JavaScript functions
│   ├── uploads/          # Uploaded images
│   └── audio/            # Generated audio files
├── templates/            # HTML templates
│   ├── base.html         # Base template
│   ├── index.html        # Homepage
│   ├── camera.html       # Camera page
│   └── upload.html       # Upload page
└── utils/                # Utility modules
    ├── model_loader.py   # Model loading and prediction
    ├── image_processor.py # Image processing
    └── audio_generator.py # Audio generation
```

## 🔧 Features

### 🏠 **Homepage**
- Giới thiệu hệ thống
- Thống kê và thông tin
- Navigation đến các tính năng

### 📷 **Camera Page**
- Real-time camera access
- Capture và phân tích ảnh
- Hiển thị kết quả ngay lập tức

### 📤 **Upload Page**
- Upload ảnh từ file
- Drag & drop support
- Validation file type và size

### 🧠 **AI Features**
- Load model TensorFlow
- Image preprocessing
- Top-3 predictions với confidence
- Vietnamese class names
- Disposal instructions

### 🔊 **Audio Features**
- Text-to-Speech (gTTS)
- Offline TTS (pyttsx3)
- Vietnamese audio guidance
- Fallback text display

## 🛠️ API Endpoints

### `POST /api/predict`
Phân tích ảnh và trả về kết quả

**Request:**
- File upload: `multipart/form-data` với field `image`
- Camera data: JSON với `image_data` (base64)

**Response:**
```json
{
  "image_path": "path/to/image",
  "predictions": [
    {
      "rank": 1,
      "class": "plastic",
      "class_vietnamese": "Nhựa",
      "confidence": 0.95,
      "percentage": 95.0,
      "disposal_instruction": "Thùng rác tái chế - nhựa"
    }
  ],
  "image_url": "/static/uploads/image.jpg",
  "audio_url": "/static/audio/audio.mp3",
  "timestamp": "2024-01-01T12:00:00"
}
```

### `GET /api/model_info`
Thông tin về model

### `GET /api/classes`
Danh sách classes được hỗ trợ

## 🔧 Configuration

### Environment Variables
Tạo file `.env` (optional):
```
FLASK_ENV=development
FLASK_DEBUG=True
SECRET_KEY=your_secret_key
MAX_CONTENT_LENGTH=16777216  # 16MB
```

### Model Paths
Điều chỉnh đường dẫn model trong `app.py`:
```python
model_path = '../models/smarttrash_model.h5'
labels_path = '../models/labels.txt'
model_info_path = '../models/model_info.json'
```

## 🚨 Troubleshooting

### Model không load được
```
❌ Error: Model file not found
```
**Giải pháp:**
- Kiểm tra file model có tồn tại trong `../models/`
- Đảm bảo đường dẫn đúng trong `app.py`

### Camera không hoạt động
```
❌ Error: Cannot access camera
```
**Giải pháp:**
- Kiểm tra quyền truy cập camera
- Sử dụng HTTPS cho production
- Test trên localhost trước

### Audio không phát được
```
⚠️ gTTS not available
```
**Giải pháp:**
- Cài đặt: `pip install gtts pyttsx3`
- Kiểm tra kết nối internet (cho gTTS)
- Fallback sẽ hiển thị text

### Memory Error
```
❌ Out of memory
```
**Giải pháp:**
- Giảm kích thước ảnh upload
- Tăng RAM hoặc sử dụng TensorFlow Lite
- Optimize model

## 📱 Mobile Support

Web app hỗ trợ responsive design:
- Camera access trên mobile
- Touch-friendly interface
- Optimized cho màn hình nhỏ

## 🔒 Security

- File upload validation
- Size limits (16MB)
- Secure filename handling
- CSRF protection

## 🚀 Production Deployment

### Using Gunicorn
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### Using Docker
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5000
CMD ["python", "app.py"]
```

### Environment Setup
- Set `FLASK_ENV=production`
- Use proper secret key
- Configure reverse proxy (nginx)
- Enable HTTPS

## 📊 Performance

- Model inference: <1s
- Image upload: <5s
- Audio generation: <3s
- Memory usage: ~500MB

## 🔄 Updates

Để update model:
1. Replace files trong `../models/`
2. Restart application
3. Clear browser cache

## 📞 Support

Nếu gặp vấn đề:
1. Kiểm tra console logs
2. Verify model files
3. Test với ảnh mẫu
4. Check network connectivity
