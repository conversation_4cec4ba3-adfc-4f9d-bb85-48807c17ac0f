{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "imports", "outputId": "80502e18-5f11-4ac0-ab32-e2a0710ada39"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ TensorFlow version: 2.18.0\n", "✅ GPU available: True\n"]}], "source": ["# Import libraries (sử dụng packages có sẵn trong Colab)\n", "import tensorflow as tf\n", "from tensorflow import keras\n", "from tensorflow.keras import layers\n", "from tensorflow.keras.applications import MobileNetV2\n", "from tensorflow.keras.preprocessing.image import ImageDataGenerator\n", "from tensorflow.keras.optimizers import Adam\n", "from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau\n", "\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import os\n", "import json\n", "from google.colab import drive, files\n", "import zipfile\n", "\n", "# Set seeds\n", "np.random.seed(42)\n", "tf.random.set_seed(42)\n", "\n", "print(f\"✅ TensorFlow version: {tf.__version__}\")\n", "print(f\"✅ GPU available: {len(tf.config.list_physical_devices('GPU')) > 0}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "mount_drive", "outputId": "ad012d89-f9cb-4362-a335-2d469692523f"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Drive already mounted at /content/drive; to attempt to forcibly remount, call drive.mount(\"/content/drive\", force_remount=True).\n", "📁 Base path: /content/drive/MyDrive/SmartTrash\n", "📁 Dataset path: /content/drive/MyDrive/SmartTrash/rubbish-data\n", "💾 Model save path: /content/drive/MyDrive/SmartTrash/models\n", "🏷️ Classes: 10\n", "\n", "📦 Dataset folder not found. Checking for zip file...\n", "✅ Found zip file: /content/drive/MyDrive/SmartTrash/rubbish-data.zip\n", "🔄 Extracting dataset...\n", "✅ Dataset extracted successfully!\n", "\n", "✅ Dataset found!\n", "   📊 train: 7975 images\n", "   📊 val: 1050 images\n", "   📊 test: 1113 images\n", "   🎯 Total: 10138 images\n", "\n", "🚀 Ready for training!\n"]}], "source": ["# Mount Google Drive\n", "drive.mount('/content/drive')\n", "\n", "# ⚠️ QUAN TRỌNG: Đi<PERSON>u chỉnh đường dẫn này theo cấu trúc Drive của bạn\n", "BASE_PATH = '/content/drive/MyDrive/SmartTrash'  # Thay đổi đường dẫn này\n", "DATASET_PATH = os.path.join(BASE_PATH, 'rubbish-data')\n", "MODEL_SAVE_PATH = os.path.join(BASE_PATH, 'models')\n", "ZIP_PATH = os.path.join(BASE_PATH, 'rubbish-data.zip')  # Đường dẫn file zip\n", "\n", "# T<PERSON><PERSON> thư mục models\n", "os.makedirs(MODEL_SAVE_PATH, exist_ok=True)\n", "\n", "# Class names\n", "CLASS_NAMES = ['battery', 'biological', 'brown-glass', 'cardboard', 'green-glass',\n", "               'metal', 'paper', 'plastic', 'trash', 'white-glass']\n", "\n", "print(f\"📁 Base path: {BASE_PATH}\")\n", "print(f\"📁 Dataset path: {DATASET_PATH}\")\n", "print(f\"💾 Model save path: {MODEL_SAVE_PATH}\")\n", "print(f\"🏷️ Classes: {len(CLASS_NAMES)}\")\n", "\n", "# Kiểm tra và giải nén dataset nếu cần\n", "if not os.path.exists(DATASET_PATH):\n", "    print(\"\\n📦 Dataset folder not found. Checking for zip file...\")\n", "\n", "    if os.path.exists(ZIP_PATH):\n", "        print(f\"✅ Found zip file: {ZIP_PATH}\")\n", "        print(\"🔄 Extracting dataset...\")\n", "\n", "        import zipfile\n", "        with zipfile.ZipFile(ZIP_PATH, 'r') as zip_ref:\n", "            zip_ref.extractall(BASE_PATH)\n", "\n", "        print(\"✅ Dataset extracted successfully!\")\n", "    else:\n", "        print(f\"❌ Zip file not found: {ZIP_PATH}\")\n", "        print(\"💡 Please upload rubbish-data.zip to your Drive folder\")\n", "        print(\"💡 Or create rubbish-data folder with train/val/test subfolders\")\n", "\n", "# Kiểm tra dataset sau khi giải nén\n", "if os.path.exists(DATASET_PATH):\n", "    print(\"\\n✅ Dataset found!\")\n", "    total_images = 0\n", "    for split in ['train', 'val', 'test']:\n", "        split_path = os.path.join(DATASET_PATH, split)\n", "        if os.path.exists(split_path):\n", "            count = sum([len(os.listdir(os.path.join(split_path, cls)))\n", "                        for cls in CLASS_NAMES if os.path.exists(os.path.join(split_path, cls))])\n", "            total_images += count\n", "            print(f\"   📊 {split}: {count} images\")\n", "    print(f\"   🎯 Total: {total_images} images\")\n", "\n", "    if total_images > 0:\n", "        print(\"\\n🚀 Ready for training!\")\n", "    else:\n", "        print(\"\\n⚠️ No images found in dataset folders\")\n", "else:\n", "    print(\"\\n❌ Dataset still not found after extraction attempt\")\n", "    print(\"\\n🔍 Available files in base directory:\")\n", "    if os.path.exists(BASE_PATH):\n", "        for item in os.listdir(BASE_PATH):\n", "            print(f\"   - {item}\")\n", "    else:\n", "        print(f\"   Base path doesn't exist: {BASE_PATH}\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "data_generators", "outputId": "91c01519-e181-4939-ba10-e25021d88f21"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 7975 images belonging to 10 classes.\n", "Found 1050 images belonging to 10 classes.\n", "Found 1113 images belonging to 10 classes.\n", "✅ Training samples: 7975\n", "✅ Validation samples: 1050\n", "✅ Test samples: 1113\n"]}], "source": ["# <PERSON><PERSON><PERSON> hình\n", "IMG_SIZE = 224\n", "BATCH_SIZE = 32\n", "NUM_CLASSES = len(CLASS_NAMES)\n", "\n", "# Data generators\n", "train_datagen = ImageDataGenerator(\n", "    rescale=1./255,\n", "    rotation_range=20,\n", "    width_shift_range=0.2,\n", "    height_shift_range=0.2,\n", "    shear_range=0.2,\n", "    zoom_range=0.2,\n", "    horizontal_flip=True,\n", "    fill_mode='nearest'\n", ")\n", "\n", "val_test_datagen = ImageDataGenerator(rescale=1./255)\n", "\n", "# Create generators\n", "train_generator = train_datagen.flow_from_directory(\n", "    os.path.join(DATASET_PATH, 'train'),\n", "    target_size=(IMG_SIZE, IMG_SIZE),\n", "    batch_size=BATCH_SIZE,\n", "    class_mode='categorical',\n", "    classes=CLASS_NAMES,\n", "    shuffle=True\n", ")\n", "\n", "val_generator = val_test_datagen.flow_from_directory(\n", "    os.path.join(DATASET_PATH, 'val'),\n", "    target_size=(IMG_SIZE, IMG_SIZE),\n", "    batch_size=BATCH_SIZE,\n", "    class_mode='categorical',\n", "    classes=CLASS_NAMES,\n", "    shuffle=False\n", ")\n", "\n", "test_generator = val_test_datagen.flow_from_directory(\n", "    os.path.join(DATASET_PATH, 'test'),\n", "    target_size=(IMG_SIZE, IMG_SIZE),\n", "    batch_size=BATCH_SIZE,\n", "    class_mode='categorical',\n", "    classes=CLASS_NAMES,\n", "    shuffle=False\n", ")\n", "\n", "print(f\"✅ Training samples: {train_generator.samples}\")\n", "print(f\"✅ Validation samples: {val_generator.samples}\")\n", "print(f\"✅ Test samples: {test_generator.samples}\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "create_model", "outputId": "73cca566-6b8c-4af3-b378-bc2e693083a0"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading data from https://storage.googleapis.com/tensorflow/keras-applications/mobilenet_v2/mobilenet_v2_weights_tf_dim_ordering_tf_kernels_1.0_224_no_top.h5\n", "\u001b[1m9406464/9406464\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 0us/step\n", "✅ Model created and compiled!\n", "📊 Total parameters: 2,921,034\n"]}], "source": ["# Tạo model với MobileNetV2\n", "def create_model():\n", "    base_model = MobileNetV2(\n", "        weights='imagenet',\n", "        include_top=False,\n", "        input_shape=(IMG_SIZE, IMG_SIZE, 3)\n", "    )\n", "\n", "    # Freeze base model\n", "    base_model.trainable = False\n", "\n", "    # Add custom head\n", "    model = keras.Sequential([\n", "        base_model,\n", "        layers.GlobalAveragePooling2D(),\n", "        layers.Dropout(0.3),\n", "        layers.Dense(512, activation='relu'),\n", "        layers.BatchNormalization(),\n", "        layers.Dropout(0.5),\n", "        layers.Dense(NUM_CLASSES, activation='softmax')\n", "    ])\n", "\n", "    return model\n", "\n", "# Create and compile model\n", "model = create_model()\n", "model.compile(\n", "    optimizer=<PERSON>(learning_rate=0.001),\n", "    loss='categorical_crossentropy',\n", "    metrics=['accuracy']\n", ")\n", "\n", "print(\"✅ Model created and compiled!\")\n", "print(f\"📊 Total parameters: {model.count_params():,}\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "train_model", "outputId": "a0384ff9-c34b-4723-ed7c-f4f720494972"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 Starting training for 30 epochs...\n", "📊 Steps per epoch: 249\n", "📊 Validation steps: 32\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.11/dist-packages/keras/src/trainers/data_adapters/py_dataset_adapter.py:121: UserWarning: Your `PyDataset` class should call `super().__init__(**kwargs)` in its constructor. `**kwargs` can include `workers`, `use_multiprocessing`, `max_queue_size`. Do not pass these arguments to `fit()`, as they will be ignored.\n", "  self._warn_if_super_not_called()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 1/30\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 576ms/step - accuracy: 0.6094 - loss: 1.3585\n", "Epoch 1: val_accuracy improved from -inf to 0.87793, saving model to /content/drive/MyDrive/SmartTrash/models/best_model.h5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:absl:You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. \n"]}, {"name": "stdout", "output_type": "stream", "text": ["\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\r\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m166s\u001b[0m 614ms/step - accuracy: 0.6098 - loss: 1.3570 - val_accuracy: 0.8779 - val_loss: 0.4122 - learning_rate: 0.0010\n", "Epoch 2/30\n", "\u001b[1m  1/249\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m9s\u001b[0m 38ms/step - accuracy: 0.8750 - loss: 0.3875"]}, {"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.11/dist-packages/keras/src/trainers/epoch_iterator.py:107: UserWarning: Your input ran out of data; interrupting training. Make sure that your dataset or generator can generate at least `steps_per_epoch * epochs` batches. You may need to use the `.repeat()` function when building your dataset.\n", "  self._interrupted_warning()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Epoch 2: val_accuracy improved from 0.87793 to 0.87891, saving model to /content/drive/MyDrive/SmartTrash/models/best_model.h5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:absl:You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. \n"]}, {"name": "stdout", "output_type": "stream", "text": ["\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\r\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m6s\u001b[0m 25ms/step - accuracy: 0.8750 - loss: 0.3875 - val_accuracy: 0.8789 - val_loss: 0.4136 - learning_rate: 0.0010\n", "Epoch 3/30\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 570ms/step - accuracy: 0.7983 - loss: 0.6152\n", "Epoch 3: val_accuracy improved from 0.87891 to 0.88281, saving model to /content/drive/MyDrive/SmartTrash/models/best_model.h5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:absl:You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. \n"]}, {"name": "stdout", "output_type": "stream", "text": ["\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\r\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m147s\u001b[0m 590ms/step - accuracy: 0.7983 - loss: 0.6152 - val_accuracy: 0.8828 - val_loss: 0.3913 - learning_rate: 0.0010\n", "Epoch 4/30\n", "\u001b[1m  1/249\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m10s\u001b[0m 43ms/step - accuracy: 0.6875 - loss: 0.9766\n", "Epoch 4: val_accuracy did not improve from 0.88281\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 20ms/step - accuracy: 0.6875 - loss: 0.9766 - val_accuracy: 0.8809 - val_loss: 0.3924 - learning_rate: 0.0010\n", "Epoch 5/30\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 531ms/step - accuracy: 0.8211 - loss: 0.5434\n", "Epoch 5: val_accuracy improved from 0.88281 to 0.89160, saving model to /content/drive/MyDrive/SmartTrash/models/best_model.h5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:absl:You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. \n"]}, {"name": "stdout", "output_type": "stream", "text": ["\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\r\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m202s\u001b[0m 613ms/step - accuracy: 0.8211 - loss: 0.5434 - val_accuracy: 0.8916 - val_loss: 0.3647 - learning_rate: 0.0010\n", "Epoch 6/30\n", "\u001b[1m  1/249\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m10s\u001b[0m 43ms/step - accuracy: 0.8125 - loss: 0.6481\n", "Epoch 6: val_accuracy improved from 0.89160 to 0.89258, saving model to /content/drive/MyDrive/SmartTrash/models/best_model.h5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:absl:You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. \n"]}, {"name": "stdout", "output_type": "stream", "text": ["\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\r\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 19ms/step - accuracy: 0.8125 - loss: 0.6481 - val_accuracy: 0.8926 - val_loss: 0.3656 - learning_rate: 0.0010\n", "Epoch 7/30\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 533ms/step - accuracy: 0.8319 - loss: 0.5000\n", "Epoch 7: val_accuracy did not improve from 0.89258\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m182s\u001b[0m 552ms/step - accuracy: 0.8319 - loss: 0.5000 - val_accuracy: 0.8887 - val_loss: 0.3509 - learning_rate: 0.0010\n", "Epoch 8/30\n", "\u001b[1m  1/249\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m10s\u001b[0m 42ms/step - accuracy: 0.8438 - loss: 0.7918\n", "Epoch 8: val_accuracy did not improve from 0.89258\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 18ms/step - accuracy: 0.8438 - loss: 0.7918 - val_accuracy: 0.8906 - val_loss: 0.3515 - learning_rate: 0.0010\n", "Epoch 9/30\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 541ms/step - accuracy: 0.8448 - loss: 0.4702\n", "Epoch 9: val_accuracy improved from 0.89258 to 0.89941, saving model to /content/drive/MyDrive/SmartTrash/models/best_model.h5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:absl:You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. \n"]}, {"name": "stdout", "output_type": "stream", "text": ["\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\r\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m143s\u001b[0m 575ms/step - accuracy: 0.8448 - loss: 0.4703 - val_accuracy: 0.8994 - val_loss: 0.3516 - learning_rate: 0.0010\n", "Epoch 10/30\n", "\u001b[1m  1/249\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m11s\u001b[0m 48ms/step - accuracy: 0.9062 - loss: 0.4606\n", "Epoch 10: val_accuracy improved from 0.89941 to 0.90039, saving model to /content/drive/MyDrive/SmartTrash/models/best_model.h5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:absl:You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. \n"]}, {"name": "stdout", "output_type": "stream", "text": ["\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\r\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m9s\u001b[0m 38ms/step - accuracy: 0.9062 - loss: 0.4606 - val_accuracy: 0.9004 - val_loss: 0.3514 - learning_rate: 0.0010\n", "Epoch 11/30\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 540ms/step - accuracy: 0.8457 - loss: 0.4496\n", "Epoch 11: val_accuracy did not improve from 0.90039\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m142s\u001b[0m 568ms/step - accuracy: 0.8457 - loss: 0.4496 - val_accuracy: 0.8994 - val_loss: 0.3215 - learning_rate: 0.0010\n", "Epoch 12/30\n", "\u001b[1m  1/249\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m13s\u001b[0m 55ms/step - accuracy: 0.8750 - loss: 0.3033\n", "Epoch 12: val_accuracy did not improve from 0.90039\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 20ms/step - accuracy: 0.8750 - loss: 0.3033 - val_accuracy: 0.9004 - val_loss: 0.3210 - learning_rate: 0.0010\n", "Epoch 13/30\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 542ms/step - accuracy: 0.8559 - loss: 0.4255\n", "Epoch 13: val_accuracy did not improve from 0.90039\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m140s\u001b[0m 561ms/step - accuracy: 0.8559 - loss: 0.4255 - val_accuracy: 0.8965 - val_loss: 0.3107 - learning_rate: 0.0010\n", "Epoch 14/30\n", "\u001b[1m  1/249\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m10s\u001b[0m 42ms/step - accuracy: 0.8438 - loss: 0.3174\n", "Epoch 14: val_accuracy did not improve from 0.90039\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 19ms/step - accuracy: 0.8438 - loss: 0.3174 - val_accuracy: 0.8955 - val_loss: 0.3099 - learning_rate: 0.0010\n", "Epoch 15/30\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 536ms/step - accuracy: 0.8529 - loss: 0.4256\n", "Epoch 15: val_accuracy improved from 0.90039 to 0.90430, saving model to /content/drive/MyDrive/SmartTrash/models/best_model.h5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:absl:You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. \n"]}, {"name": "stdout", "output_type": "stream", "text": ["\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\r\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m143s\u001b[0m 576ms/step - accuracy: 0.8529 - loss: 0.4256 - val_accuracy: 0.9043 - val_loss: 0.2906 - learning_rate: 0.0010\n", "Epoch 16/30\n", "\u001b[1m  1/249\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m10s\u001b[0m 41ms/step - accuracy: 0.7188 - loss: 0.5653\n", "Epoch 16: val_accuracy improved from 0.90430 to 0.90527, saving model to /content/drive/MyDrive/SmartTrash/models/best_model.h5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:absl:You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. \n"]}, {"name": "stdout", "output_type": "stream", "text": ["\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\r\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 20ms/step - accuracy: 0.7188 - loss: 0.5653 - val_accuracy: 0.9053 - val_loss: 0.2897 - learning_rate: 0.0010\n", "Epoch 17/30\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 561ms/step - accuracy: 0.8579 - loss: 0.4242\n", "Epoch 17: val_accuracy did not improve from 0.90527\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m199s\u001b[0m 585ms/step - accuracy: 0.8579 - loss: 0.4242 - val_accuracy: 0.8994 - val_loss: 0.3080 - learning_rate: 0.0010\n", "Epoch 18/30\n", "\u001b[1m  1/249\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m10s\u001b[0m 43ms/step - accuracy: 0.7188 - loss: 0.5203\n", "Epoch 18: val_accuracy did not improve from 0.90527\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m8s\u001b[0m 34ms/step - accuracy: 0.7188 - loss: 0.5203 - val_accuracy: 0.8984 - val_loss: 0.3088 - learning_rate: 0.0010\n", "Epoch 19/30\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 572ms/step - accuracy: 0.8674 - loss: 0.3915\n", "Epoch 19: val_accuracy did not improve from 0.90527\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m202s\u001b[0m 620ms/step - accuracy: 0.8674 - loss: 0.3916 - val_accuracy: 0.9004 - val_loss: 0.2906 - learning_rate: 0.0010\n", "Epoch 20/30\n", "\u001b[1m  1/249\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m10s\u001b[0m 42ms/step - accuracy: 0.8125 - loss: 0.4606\n", "Epoch 20: val_accuracy did not improve from 0.90527\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 22ms/step - accuracy: 0.8125 - loss: 0.4606 - val_accuracy: 0.8994 - val_loss: 0.2906 - learning_rate: 0.0010\n", "Epoch 21/30\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 528ms/step - accuracy: 0.8670 - loss: 0.3947\n", "Epoch 21: val_accuracy improved from 0.90527 to 0.91016, saving model to /content/drive/MyDrive/SmartTrash/models/best_model.h5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:absl:You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. \n"]}, {"name": "stdout", "output_type": "stream", "text": ["\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\r\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m138s\u001b[0m 553ms/step - accuracy: 0.8670 - loss: 0.3946 - val_accuracy: 0.9102 - val_loss: 0.2746 - learning_rate: 0.0010\n", "Epoch 22/30\n", "\u001b[1m  1/249\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m10s\u001b[0m 44ms/step - accuracy: 0.8750 - loss: 0.3239\n", "Epoch 22: val_accuracy did not improve from 0.91016\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m4s\u001b[0m 17ms/step - accuracy: 0.8750 - loss: 0.3239 - val_accuracy: 0.9102 - val_loss: 0.2759 - learning_rate: 0.0010\n", "Epoch 23/30\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 550ms/step - accuracy: 0.8757 - loss: 0.3693\n", "Epoch 23: val_accuracy did not improve from 0.91016\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m142s\u001b[0m 569ms/step - accuracy: 0.8757 - loss: 0.3693 - val_accuracy: 0.9092 - val_loss: 0.2883 - learning_rate: 0.0010\n", "Epoch 24/30\n", "\u001b[1m  1/249\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m9s\u001b[0m 40ms/step - accuracy: 0.8438 - loss: 0.4016\n", "Epoch 24: val_accuracy did not improve from 0.91016\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 20ms/step - accuracy: 0.8438 - loss: 0.4016 - val_accuracy: 0.9092 - val_loss: 0.2878 - learning_rate: 0.0010\n", "Epoch 25/30\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 542ms/step - accuracy: 0.8682 - loss: 0.3799\n", "Epoch 25: val_accuracy did not improve from 0.91016\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m141s\u001b[0m 564ms/step - accuracy: 0.8682 - loss: 0.3799 - val_accuracy: 0.9043 - val_loss: 0.2846 - learning_rate: 0.0010\n", "Epoch 26/30\n", "\u001b[1m  1/249\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m10s\u001b[0m 42ms/step - accuracy: 0.8750 - loss: 0.3014\n", "Epoch 26: val_accuracy did not improve from 0.91016\n", "\n", "Epoch 26: ReduceLROnPlateau reducing learning rate to 0.0005000000237487257.\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m4s\u001b[0m 17ms/step - accuracy: 0.8750 - loss: 0.3014 - val_accuracy: 0.9062 - val_loss: 0.2841 - learning_rate: 0.0010\n", "Epoch 27/30\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 556ms/step - accuracy: 0.8758 - loss: 0.3620\n", "Epoch 27: val_accuracy did not improve from 0.91016\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m202s\u001b[0m 812ms/step - accuracy: 0.8759 - loss: 0.3619 - val_accuracy: 0.9062 - val_loss: 0.2611 - learning_rate: 5.0000e-04\n", "Epoch 28/30\n", "\u001b[1m  1/249\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m10s\u001b[0m 43ms/step - accuracy: 0.8750 - loss: 0.3532\n", "Epoch 28: val_accuracy did not improve from 0.91016\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m5s\u001b[0m 22ms/step - accuracy: 0.8750 - loss: 0.3532 - val_accuracy: 0.9082 - val_loss: 0.2605 - learning_rate: 5.0000e-04\n", "Epoch 29/30\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 546ms/step - accuracy: 0.8834 - loss: 0.3297\n", "Epoch 29: val_accuracy improved from 0.91016 to 0.92188, saving model to /content/drive/MyDrive/SmartTrash/models/best_model.h5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:absl:You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. \n"]}, {"name": "stdout", "output_type": "stream", "text": ["\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\r\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m143s\u001b[0m 572ms/step - accuracy: 0.8834 - loss: 0.3297 - val_accuracy: 0.9219 - val_loss: 0.2441 - learning_rate: 5.0000e-04\n", "Epoch 30/30\n", "\u001b[1m  1/249\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m10s\u001b[0m 42ms/step - accuracy: 0.9062 - loss: 0.4889\n", "Epoch 30: val_accuracy did not improve from 0.92188\n", "\u001b[1m249/249\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m4s\u001b[0m 18ms/step - accuracy: 0.9062 - loss: 0.4889 - val_accuracy: 0.9199 - val_loss: 0.2444 - learning_rate: 5.0000e-04\n", "Restoring model weights from the end of the best epoch: 29.\n", "\n", "🎉 Training completed!\n"]}], "source": ["# Setup callbacks\n", "callbacks = [\n", "    EarlyStopping(\n", "        monitor='val_accuracy',\n", "        patience=10,\n", "        restore_best_weights=True,\n", "        verbose=1\n", "    ),\n", "    ModelCheckpoint(\n", "        filepath=os.path.join(MODEL_SAVE_PATH, 'best_model.h5'),\n", "        monitor='val_accuracy',\n", "        save_best_only=True,\n", "        verbose=1\n", "    ),\n", "    ReduceLROnPlateau(\n", "        monitor='val_loss',\n", "        factor=0.5,\n", "        patience=5,\n", "        min_lr=1e-7,\n", "        verbose=1\n", "    )\n", "]\n", "\n", "# Training parameters\n", "EPOCHS = 30  # Gi<PERSON>m epochs để training n<PERSON>h hơn\n", "steps_per_epoch = train_generator.samples // BATCH_SIZE\n", "validation_steps = val_generator.samples // BATCH_SIZE\n", "\n", "print(f\"🚀 Starting training for {EPOCHS} epochs...\")\n", "print(f\"📊 Steps per epoch: {steps_per_epoch}\")\n", "print(f\"📊 Validation steps: {validation_steps}\")\n", "\n", "# Train model\n", "history = model.fit(\n", "    train_generator,\n", "    steps_per_epoch=steps_per_epoch,\n", "    epochs=EPOCHS,\n", "    validation_data=val_generator,\n", "    validation_steps=validation_steps,\n", "    callbacks=callbacks,\n", "    verbose=1\n", ")\n", "\n", "print(\"\\n🎉 Training completed!\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 511}, "id": "evaluate_model", "outputId": "c62603b0-495d-4ad9-cd9e-3038ab43916d"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Evaluating model on test set...\n", "\u001b[1m35/35\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m9s\u001b[0m 253ms/step - accuracy: 0.9472 - loss: 0.1798\n", "\n", "🎯 TEST RESULTS:\n", "   • Test Loss: 0.2704\n", "   • Test Accuracy: 0.9093 (90.93%)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Evaluate model\n", "print(\"📊 Evaluating model on test set...\")\n", "test_loss, test_accuracy = model.evaluate(test_generator, verbose=1)\n", "\n", "print(f\"\\n🎯 TEST RESULTS:\")\n", "print(f\"   • Test Loss: {test_loss:.4f}\")\n", "print(f\"   • Test Accuracy: {test_accuracy:.4f} ({test_accuracy*100:.2f}%)\")\n", "\n", "# Plot training history\n", "plt.figure(figsize=(12, 4))\n", "\n", "plt.subplot(1, 2, 1)\n", "plt.plot(history.history['accuracy'], label='Training Accuracy')\n", "plt.plot(history.history['val_accuracy'], label='Validation Accuracy')\n", "plt.title('Model Accuracy')\n", "plt.xlabel('Epoch')\n", "plt.ylabel('Accuracy')\n", "plt.legend()\n", "plt.grid(True)\n", "\n", "plt.subplot(1, 2, 2)\n", "plt.plot(history.history['loss'], label='Training Loss')\n", "plt.plot(history.history['val_loss'], label='Validation Loss')\n", "plt.title('Model Loss')\n", "plt.xlabel('Epoch')\n", "plt.ylabel('Loss')\n", "plt.legend()\n", "plt.grid(True)\n", "\n", "plt.tight_layout()\n", "plt.savefig(os.path.join(MODEL_SAVE_PATH, 'training_history.png'), dpi=150, bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "save_model", "outputId": "3cc071c5-6a2a-40cd-85da-25538879f8ec"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:absl:You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. \n"]}, {"name": "stdout", "output_type": "stream", "text": ["💾 Saving model and files...\n", "✅ Model saved: /content/drive/MyDrive/SmartTrash/models/smarttrash_model.h5\n", "✅ Labels saved: /content/drive/MyDrive/SmartTrash/models/labels.txt\n", "✅ Model info saved: /content/drive/MyDrive/SmartTrash/models/model_info.json\n", "\n", "🎉 All files saved successfully!\n"]}], "source": ["# Save model và files\n", "print(\"💾 Saving model and files...\")\n", "\n", "# Save model\n", "model_path = os.path.join(MODEL_SAVE_PATH, 'smarttrash_model.h5')\n", "model.save(model_path)\n", "print(f\"✅ Model saved: {model_path}\")\n", "\n", "# Save labels\n", "labels_path = os.path.join(MODEL_SAVE_PATH, 'labels.txt')\n", "with open(labels_path, 'w') as f:\n", "    for label in CLASS_NAMES:\n", "        f.write(f\"{label}\\n\")\n", "print(f\"✅ Labels saved: {labels_path}\")\n", "\n", "# Save model info\n", "model_info = {\n", "    'model_name': 'SmartTrash Classifier',\n", "    'architecture': 'MobileNetV2 + Custom Head',\n", "    'input_size': [IMG_SIZE, IMG_SIZE, 3],\n", "    'num_classes': NUM_CLASSES,\n", "    'class_names': CLASS_NAMES,\n", "    'test_accuracy': float(test_accuracy),\n", "    'preprocessing': {\n", "        'rescale': '1./255',\n", "        'target_size': [IMG_SIZE, IMG_SIZE]\n", "    },\n", "    'training_info': {\n", "        'batch_size': BATCH_SIZE,\n", "        'epochs_trained': len(history.history['accuracy']),\n", "        'optimizer': '<PERSON>',\n", "        'loss': 'categorical_crossentropy'\n", "    }\n", "}\n", "\n", "info_path = os.path.join(MODEL_SAVE_PATH, 'model_info.json')\n", "with open(info_path, 'w') as f:\n", "    json.dump(model_info, f, indent=2)\n", "print(f\"✅ Model info saved: {info_path}\")\n", "\n", "print(\"\\n🎉 All files saved successfully!\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 225}, "id": "download_model", "outputId": "5ccb09bd-45c1-45c7-f025-9634457f308a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📦 Creating model package for download...\n", "✅ Package created: /content/drive/MyDrive/SmartTrash/models/smarttrash_model_package.zip\n"]}, {"data": {"application/javascript": "\n    async function download(id, filename, size) {\n      if (!google.colab.kernel.accessAllowed) {\n        return;\n      }\n      const div = document.createElement('div');\n      const label = document.createElement('label');\n      label.textContent = `Downloading \"${filename}\": `;\n      div.appendChild(label);\n      const progress = document.createElement('progress');\n      progress.max = size;\n      div.appendChild(progress);\n      document.body.appendChild(div);\n\n      const buffers = [];\n      let downloaded = 0;\n\n      const channel = await google.colab.kernel.comms.open(id);\n      // Send a message to notify the kernel that we're ready.\n      channel.send({})\n\n      for await (const message of channel.messages) {\n        // Send a message to notify the kernel that we're ready.\n        channel.send({})\n        if (message.buffers) {\n          for (const buffer of message.buffers) {\n            buffers.push(buffer);\n            downloaded += buffer.byteLength;\n            progress.value = downloaded;\n          }\n        }\n      }\n      const blob = new Blob(buffers, {type: 'application/binary'});\n      const a = document.createElement('a');\n      a.href = window.URL.createObjectURL(blob);\n      a.download = filename;\n      div.appendChild(a);\n      a.click();\n      div.remove();\n    }\n  ", "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/javascript": "download(\"download_4568bd9e-3f5c-4a9c-a731-b8a2adb84add\", \"smarttrash_model_package.zip\", 17482466)", "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["📥 Model package downloaded!\n", "\n", "🎯 TRAINING SUMMARY:\n", "   • Model: MobileNetV2 Transfer Learning\n", "   • Classes: 10\n", "   • Test Accuracy: 90.93%\n", "   • Epochs Trained: 30\n", "   • Files: smarttrash_model.h5, labels.txt, model_info.json\n", "\n", "🚀 Ready for web application integration!\n"]}], "source": ["# Tạo và download model package\n", "print(\"📦 Creating model package for download...\")\n", "\n", "zip_path = os.path.join(MODEL_SAVE_PATH, 'smarttrash_model_package.zip')\n", "with zipfile.ZipFile(zip_path, 'w') as zipf:\n", "    # Add model files\n", "    zipf.write(model_path, 'smarttrash_model.h5')\n", "    zipf.write(labels_path, 'labels.txt')\n", "    zipf.write(info_path, 'model_info.json')\n", "\n", "    # Add training plot if exists\n", "    plot_path = os.path.join(MODEL_SAVE_PATH, 'training_history.png')\n", "    if os.path.exists(plot_path):\n", "        zipf.write(plot_path, 'training_history.png')\n", "\n", "print(f\"✅ Package created: {zip_path}\")\n", "\n", "# Download\n", "files.download(zip_path)\n", "print(\"📥 Model package downloaded!\")\n", "\n", "print(\"\\n🎯 TRAINING SUMMARY:\")\n", "print(f\"   • Model: MobileNetV2 Transfer Learning\")\n", "print(f\"   • Classes: {NUM_CLASSES}\")\n", "print(f\"   • Test Accuracy: {test_accuracy*100:.2f}%\")\n", "print(f\"   • Epochs Trained: {len(history.history['accuracy'])}\")\n", "print(f\"   • Files: smarttrash_model.h5, labels.txt, model_info.json\")\n", "print(\"\\n🚀 Ready for web application integration!\")"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}